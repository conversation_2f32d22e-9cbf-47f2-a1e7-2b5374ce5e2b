import { Metadata } from 'next';
import ServiceTemplate from '../../site-pages/service-template';
import {
  Palette, Smartphone, TrendingUp, Search, Brain, Gauge,
  Target, Users, Rocket, BarChart3, Globe, Zap, Code,
  DollarSign, MousePointer, Share2, Heart, MessageCircle,
  Camera, Tablet, Monitor
} from "lucide-react";

// Web Design Services Service Configuration for web-design-services-southampton
// Generated using Web Design Services template for WebforLeads
const serviceConfig = {
  "serviceName": "Web Design Services",
  "serviceSlug": "web-design-services-southampton",
  "metaTitle": "Southampton Web Design Services | WebforLeads UK",
  "metaDescription": "Elevate your business with bespoke web design in Southampton. WebforLeads crafts high-converting, SEO-optimised websites for local businesses. Get a free quote!",
  "keywords": [
    "web design southampton",
    "southampton web design",
    "website design southampton",
    "web development southampton",
    "southampton digital agency",
    "bespoke web design uk",  ],
  "heroTitle": "Bespoke Web Design Services in Southampton",
  "heroSubtitle": "Crafting High-Converting Websites for Southampton Businesses",
  "heroDescription": "WebforLeads delivers cutting-edge web design solutions tailored for businesses across Southampton, from the bustling city centre to Ocean Village and Shirley. We build powerful, user-centric websites that drive leads and boost your online presence.",
  "heroBadgeText": "SOUTHAMPTON EXPERTS",
  "stats": [
    {
      "number": "500%",
      "label": "Client ROI",
      "icon": <TrendingUp className="h-6 w-6" />
    },
    {
      "number": "£1.8M+",
      "label": "Client Revenue Generated",
      "icon": <Target className="h-6 w-6" />
    },
    {
      "number": "96%",
      "label": "Client Satisfaction",
      "icon": <Users className="h-6 w-6" />
    },
    {
      "number": "24hrs",
      "label": "Average Response Time",
      "icon": <Rocket className="h-6 w-6" />
    }
  ],
  "designFeatures": [
    {
      "icon": <Brain className="h-8 w-8" />,
      "title": "Strategic Planning",
      "description": "Data-driven strategies for Southampton's competitive market.",
      "highlight": "Conversion-Focused"
    },
    {
      "icon": <Gauge className="h-8 w-8" />,
      "title": "Performance Optimisation",
      "description": "Blazing fast sites for superior user experience and SEO.",
      "highlight": "Speed & Efficiency"
    },
    {
      "icon": <Target className="h-6 w-6" />,
      "title": "Conversion-Focused Design",
      "description": "Websites built to turn visitors into valuable leads and sales.",
      "highlight": "Lead Generation"
    },
    {
      "icon": <BarChart3 className="h-6 w-6" />,
      "title": "SEO Integration",
      "description": "Built-in SEO to rank higher in local Southampton searches.",
      "highlight": "Visibility Boost"
    }
  ],
  "features": [
    {
      "icon": <Search className="h-6 w-6" />,
      "title": "SEO-Optimised Architecture",
      "description": "Ensuring your site is found by Southampton customers."
    },
    {
      "icon": <Target className="h-6 w-6" />,
      "title": "Lead Generation Focus",
      "description": "Designing pathways that convert visitors into enquiries."
    },
    {
      "icon": <Globe className="h-6 w-6" />,
      "title": "Responsive & Mobile-First",
      "description": "Flawless experience on any device, from phone to desktop."
    },
    {
      "icon": <Zap className="h-6 w-6" />,
      "title": "Fast Loading Speeds",
      "description": "Optimised for performance, reducing bounce rates."
    },
    {
      "icon": <BarChart3 className="h-6 w-6" />,
      "title": "Analytics & Reporting",
      "description": "Transparent insights into your website's performance."
    },
    {
      "icon": <Users className="h-6 w-6" />,
      "title": "User Experience (UX) Design",
      "description": "Intuitive navigation for an engaging user journey."
    }
  ],
  "packages": [
    {
      "name": "Starter Website",
      "price": "£3,500",
      "period": "one-time",
      "description": "Ideal for new businesses or those needing a professional online presence quickly in Southampton. Focus on core information and lead capture.",
      "features": [
        "5-page bespoke design",
        "Mobile-responsive",
        "Basic SEO setup",
        "Contact form integration",      ],
      "highlight": false,
      "cta": "Get Started"
    },
    {
      "name": "Growth Platform",
      "price": "£7,500",
      "period": "one-time",
      "description": "Designed for growing Southampton businesses seeking advanced features and robust lead generation capabilities.",
      "features": [
        "Up to 15-page bespoke design",
        "Advanced SEO optimisation",
        "CRM integration",
        "Blog/News section",
        "E-commerce ready (up to 10 products)",      ],
      "highlight": true,
      "cta": "Boost Your Growth"
    },
    {
      "name": "Enterprise Solution",
      "price": "Custom",
      "period": "one-time",
      "description": "Tailored for established Southampton enterprises requiring complex functionalities, custom integrations, and scalable solutions.",
      "features": [
        "Unlimited pages",
        "Advanced e-commerce solutions",
        "Custom API integrations",
        "Dedicated project manager",
        "Ongoing performance optimisation",      ],
      "highlight": false,
      "cta": "Request a Quote"
    }
  ],
  "process": [
    {
      "step": "01",
      "title": "Discovery & Strategy",
      "description": "Deep dive into your business goals and target audience in Southampton.",
      "duration": "Week 1"
    },
    {
      "step": "02",
      "title": "Design & Development",
      "description": "Crafting bespoke designs and building a robust, functional website.",
      "duration": "Week 2-3"
    },
    {
      "step": "03",
      "title": "Testing & Launch",
      "description": "Rigorous testing across devices, followed by a seamless launch.",
      "duration": "Week 4-5"
    },
    {
      "step": "04",
      "title": "Post-Launch Support",
      "description": "Ongoing optimisation, maintenance, and performance monitoring.",
      "duration": "Week 6+"
    }
  ],
  "featuredTestimonial": {
    "quote": "WebforLeads transformed our outdated website into a powerful lead-generating machine. Our online enquiries from the Southampton area have skyrocketed, and their team was incredibly professional and insightful throughout the entire process. A truly exceptional service!",
    "name": "Sarah Jenkins",
    "role": "Marketing Director",
    "company": "Solent Marine Supplies",
    "rating": 5,
    "result": "340%",
    "resultLabel": "Increase in Online Enquiries"
  },
  "testimonials": [
    {
      "quote": "WebforLeads delivered a stunning website that perfectly captures our brand. We've seen a significant uplift in local client engagement since launch.",
      "name": "Mark Davies",
      "role": "Managing Director",
      "company": "Hampshire Property Group",
      "industry": "Real Estate",
      "metric": "315%",
      "metricLabel": "Website Traffic Increase",
      "avatar": "SW",    },
    {
      "quote": "Our new e-commerce site from WebforLeads has revolutionised our online sales. The user experience is fantastic, and it's so easy to manage.",
      "name": "Emily Roberts",
      "role": "Owner",
      "company": "New Forest Bakery",
      "industry": "Food & Beverage",
      "metric": "200%",
      "metricLabel": "Online Sales Growth",
      "avatar": "ER",    },
    {
      "quote": "From concept to launch, WebforLeads provided an outstanding service. Our website now truly reflects our expertise and attracts more local customers.",
      "name": "David Price",
      "role": "Operations Manager",
      "company": "Portswood Auto Centre",
      "industry": "Automotive",
      "metric": "400%",
      "metricLabel": "New Customer Enquiries",
      "avatar": "DP",    }
  ],
  "faqCategories": [
    {
      "category": "Process & Timeline",
      "icon": <Rocket className="h-6 w-6" />,
      "questions": [
        {
          "q": "How long does a typical web design project take?",
          "a": "Most standard web design projects for Southampton businesses are completed within 6-8 weeks, depending on complexity and client feedback. We provide a detailed timeline at the outset."
        },
        {
          "q": "What is your design revision process?",
          "a": "We include multiple revision rounds at key stages to ensure the design aligns perfectly with your vision. Your feedback is crucial to our iterative design process."
        },
        {
          "q": "Do you offer ongoing website maintenance?",
          "a": "Yes, we offer comprehensive post-launch support and maintenance packages to ensure your website remains secure, updated, and performs optimally."
        }
      ]
    },
    {
      "category": "Services & Features",
      "icon": <Target className="h-6 w-6" />,
      "questions": [
        {
          "q": "Can you build e-commerce websites for Southampton businesses?",
          "a": "Absolutely. We specialise in creating robust, secure, and user-friendly e-commerce platforms that drive online sales for businesses across Southampton and the wider UK."
        },
        {
          "q": "Are your websites mobile-friendly?",
          "a": "Every website we design is built with a mobile-first approach, ensuring it looks and functions perfectly across all devices, from smartphones to desktops."
        },
        {
          "q": "Do you integrate with third-party tools?",
          "a": "Yes, we frequently integrate websites with CRM systems, marketing automation tools, booking systems, and other essential third-party applications to streamline your operations."
        }
      ]
    },
    {
      "category": "Technical & Pricing",
      "icon": <BarChart3 className="h-6 w-6" />,
      "questions": [
        {
          "q": "What are the typical costs for web design in Southampton?",
          "a": "Our web design packages start from £3,500 for a professional, conversion-focused website. Custom solutions are priced based on specific requirements. We provide transparent quotes."
        },
        {
          "q": "Do you provide website hosting and domain registration?",
          "a": "While we don't directly provide hosting or domain registration, we can guide you through the process and recommend reliable providers to ensure optimal performance for your new website."
        },
        {
          "q": "Is SEO included in your web design services?",
          "a": "Yes, fundamental SEO best practices are integrated into every website we build, including technical SEO, keyword research, and on-page optimisation to help you rank locally in Southampton."
        }
      ]
    }
  ],
  "finalCTA": {
    "badge": "Get Started",
    "title": "Ready to Transform Your Online Presence in Southampton?",
    "description": "Partner with WebforLeads, the leading web design agency in Southampton, to build a powerful, conversion-focused website that truly represents your brand and drives measurable results."
  },
  "structuredData": {
    "serviceName": "Web Design Services",
    "description": "WebforLeads offers bespoke web design services in Southampton, crafting high-performance, SEO-optimised websites for local businesses.",
    "priceRange": "£3500-£15000+",
    "areaServed": "Southampton",
    "aggregateRating": {
      "ratingValue": "4.9",
      "reviewCount": "127"
    }
  }
};

export const metadata: Metadata = {
  title: serviceConfig.metaTitle,
  description: serviceConfig.metaDescription,
  keywords: serviceConfig.keywords.join(", "),
  openGraph: {
    title: serviceConfig.metaTitle,
    description: serviceConfig.metaDescription,
    type: 'website',
    url: `https://webforleads.uk/web-design/${serviceConfig.serviceSlug}`,
  },
  twitter: {
    card: 'summary_large_image',
    title: serviceConfig.metaTitle,
    description: serviceConfig.metaDescription,
  },
  alternates: {
    canonical: `https://webforleads.uk/web-design/${serviceConfig.serviceSlug}`,
  },
};

export default function WebDesignServicesSouthamptonPage() {
  return <ServiceTemplate config={serviceConfig} />;
}


