import { NextRequest, NextResponse } from 'next/server';
import { googleIndexingService } from '@/lib/google-indexing';

export async function GET(request: NextRequest) {
  try {
    console.log('🔄 Getting indexing status...');
    
    const { searchParams } = new URL(request.url);
    const url = searchParams.get('url');

    if (url) {
      // Get status for specific URL
      console.log(`📝 Getting status for URL: ${url}`);
      const result = await googleIndexingService.getUrlStatus(url);
      
      return NextResponse.json({
        success: result.success,
        url: result.url,
        metadata: result.metadata,
        error: result.error,
      });
    } else {
      // Get general quota and service status
      console.log('📝 Getting general indexing service status');
      
      const quotaInfo = googleIndexingService.getQuotaInfo();
      
      // Test API access
      const accessTest = await googleIndexingService.validateAccess();
      
      return NextResponse.json({
        success: true,
        quota: quotaInfo,
        apiAccess: accessTest,
        serviceInfo: {
          projectId: process.env.GOOGLE_INDEXING_PROJECT_ID,
          baseUrl: process.env.NEXT_PUBLIC_SITE_URL,
          timestamp: new Date().toISOString(),
        },
      });
    }
  } catch (error: any) {
    console.error('❌ Unexpected error in status API:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        details: error.message,
      },
      { status: 500 }
    );
  }
}
