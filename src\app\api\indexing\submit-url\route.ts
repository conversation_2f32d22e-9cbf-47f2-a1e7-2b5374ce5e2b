import { NextRequest, NextResponse } from 'next/server';
import { googleIndexingService } from '@/lib/google-indexing';

export async function POST(request: NextRequest) {
  try {
    console.log('🔄 Starting single URL indexing submission...');
    
    const body = await request.json();
    const { url, type = 'URL_UPDATED' } = body;

    // Validate input
    if (!url) {
      console.log('❌ Missing URL in request');
      return NextResponse.json(
        { success: false, error: 'URL is required' },
        { status: 400 }
      );
    }

    if (type !== 'URL_UPDATED' && type !== 'URL_DELETED') {
      console.log('❌ Invalid type in request:', type);
      return NextResponse.json(
        { success: false, error: 'Type must be URL_UPDATED or URL_DELETED' },
        { status: 400 }
      );
    }

    console.log(`📝 Processing URL: ${url}`);
    console.log(`📝 Type: ${type}`);

    // Submit URL to Google Indexing API
    const result = await googleIndexingService.submitUrl(url, type);

    if (result.success) {
      console.log(`✅ Successfully submitted URL: ${url}`);
      return NextResponse.json({
        success: true,
        message: 'URL submitted successfully',
        url: result.url,
        status: result.status,
        details: result.details,
      });
    } else {
      console.log(`❌ Failed to submit URL: ${url} - ${result.error}`);
      return NextResponse.json(
        {
          success: false,
          error: result.error,
          url: result.url,
          details: result.details,
        },
        { status: 400 }
      );
    }
  } catch (error: any) {
    console.error('❌ Unexpected error in submit-url API:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        details: error.message,
      },
      { status: 500 }
    );
  }
}
