import { Metadata } from 'next';
import ServiceTemplate from '../../site-pages/service-template';
import {
  Palette, Smartphone, TrendingUp, Search, Brain, Gauge,
  Target, Users, Rocket, BarChart3, Globe, Zap, Code,
  DollarSign, MousePointer, Share2, Heart, MessageCircle,
  Camera, Tablet, Monitor
} from "lucide-react";

// Real Estate Agent Website Design Service Configuration for real-estate-agent-website-design-liverpool
// Generated using Web Design Services template for WebforLeads
const serviceConfig = {
  "serviceName": "Real Estate Agent Website Design",
  "serviceSlug": "real-estate-agent-website-design-liverpool",
  "metaTitle": "Real Estate Agent Website Design Liverpool | WebforLeads",
  "metaDescription": "Elevate your Liverpool estate agency with a high-converting website from WebforLeads. Bespoke design, seamless property listings, and powerful lead generation for Merseyside agents.",
  "keywords": [
    "real estate website design liverpool",
    "estate agent web design merseyside",
    "property website development liverpool",
    "web design for estate agents uk",
    "liverpool property marketing",
    "estate agency website development",
    "bespoke real estate websites",  ],
  "heroTitle": "Dominate the Liverpool Property Market with a High-Converting Website",
  "heroSubtitle": "Bespoke Web Design for Real Estate Agents in Liverpool & Merseyside",
  "heroDescription": "At WebforLeads, we craft stunning, lead-generating websites specifically for Liverpool's dynamic real estate sector. From the bustling city centre to the leafy suburbs of Sefton Park and beyond, our designs ensure your agency stands out, attracts more vendors, and converts more enquiries into sales. We understand the unique demands of the Merseyside property market.",
  "heroBadgeText": "Liverpool's Property Web Experts",
  "stats": [
    {
      "number": "340%",
      "label": "Average Lead Increase",
      "icon": <TrendingUp className="h-6 w-6" />
    },
    {
      "number": "£1.8M+",
      "label": "Client Revenue Generated",
      "icon": <DollarSign className="h-6 w-6" />
    },
    {
      "number": "96%",
      "label": "Client Retention Rate",
      "icon": <Heart className="h-6 w-6" />
    },
    {
      "number": "24hrs",
      "label": "Initial Concept Delivery",
      "icon": <Rocket className="h-6 w-6" />
    }
  ],
  "designFeatures": [
    {
      "icon": <Smartphone className="h-6 w-6" />,
      "title": "Mobile-First Responsiveness",
      "description": "Ensure your property listings look flawless on any device, from desktop to mobile, capturing every potential buyer or seller across Liverpool.",
      "highlight": "Seamless Viewing"
    },
    {
      "icon": <Palette className="h-6 w-6" />,
      "title": "Brand-Centric Aesthetics",
      "description": "We design websites that reflect your agency's unique brand identity, creating a memorable online presence that resonates with Liverpool's diverse clientele.",
      "highlight": "Unique Identity"
    },
    {
      "icon": <MousePointer className="h-6 w-6" />,
      "title": "Intuitive User Experience",
      "description": "Streamlined navigation and clear calls-to-action guide visitors effortlessly through your property portfolio, boosting engagement and enquiries.",
      "highlight": "Effortless Navigation"
    },
    {
      "icon": <Code className="h-6 w-6" />,
      "title": "Scalable & Future-Proof",
      "description": "Built on robust, modern frameworks, your website will grow with your agency, ready for new features and market demands in Liverpool and beyond.",
      "highlight": "Growth Ready"
    }
  ],
  "features": [
    {
      "icon": <Search className="h-6 w-6" />,
      "title": "Advanced Property Search",
      "description": "Empower users with intuitive search filters, map integration, and saved searches for properties across Liverpool, Wirral, and Merseyside."
    },
    {
      "icon": <Target className="h-6 w-6" />,
      "title": "Integrated Lead Capture",
      "description": "Strategically placed enquiry forms, valuation tools, and viewing requests to convert website visitors into qualified leads for your agency."
    },
    {
      "icon": <Globe className="h-6 w-6" />,
      "title": "CRM System Integration",
      "description": "Seamlessly connect your website with popular real estate CRM platforms for efficient lead management and client communication."
    },
    {
      "icon": <Zap className="h-6 w-6" />,
      "title": "Virtual Tour & Video Support",
      "description": "Showcase properties with immersive virtual tours and high-quality video, offering a compelling experience for potential buyers."
    },
    {
      "icon": <BarChart3 className="h-6 w-6" />,
      "title": "Performance Analytics",
      "description": "Gain insights into website traffic, user behaviour, and lead sources with comprehensive analytics reporting, tailored for your Liverpool agency."
    },
    {
      "icon": <Users className="h-6 w-6" />,
      "title": "Agent & Team Profiles",
      "description": "Highlight your expert team with professional profiles, showcasing their local knowledge and building trust with clients in the Liverpool area."
    }
  ],
  "packages": [
    {
      "name": "Starter Agent Website",
      "price": "£3,500",
      "period": "one-time",
      "description": "Ideal for new or smaller Liverpool estate agencies needing a professional online presence to showcase properties and capture initial leads.",
      "features": [
        "Bespoke 5-page design",
        "Responsive mobile layout",
        "Basic property listing integration",
        "Contact forms",      ],
      "highlight": false,
      "cta": "Get Started"
    },
    {
      "name": "Professional Agency Site",
      "price": "£7,500",
      "period": "one-time",
      "description": "Our most popular package for established Liverpool estate agents seeking advanced features, robust lead generation, and a competitive edge.",
      "features": [
        "Custom 10+ page design",
        "Advanced property search & filters",
        "CRM integration (e.g., Alto, Reapit)",
        "Virtual tour support",
        "Blog & news section",      ],
      "highlight": true,
      "cta": "View Details"
    },
    {
      "name": "Enterprise Property Portal",
      "price": "Custom",
      "period": "one-time",
      "description": "For large agencies or developers requiring a highly customised, scalable platform with unique functionalities to dominate the Liverpool property market.",
      "features": [
        "Fully bespoke development",
        "Multi-branch support",
        "Custom API integrations",
        "Advanced user portals",
        "Dedicated account manager",      ],
      "highlight": false,
      "cta": "Request Quote"
    }
  ],
  "process": [
    {
      "step": "01",
      "title": "Discovery & Strategy",
      "description": "We begin by understanding your Liverpool agency's unique goals, target audience, and competitive landscape, mapping out a bespoke strategy.",
      "duration": "Week 1"
    },
    {
      "step": "02",
      "title": "Design & Development",
      "description": "Our expert team crafts a visually stunning and highly functional website, focusing on user experience and lead conversion for the Liverpool market.",
      "duration": "Week 2-3"
    },
    {
      "step": "03",
      "title": "Content & Integration",
      "description": "We integrate your property listings, CRM, and other essential tools, ensuring all content is optimised and ready for launch.",
      "duration": "Week 4-5"
    },
    {
      "step": "04",
      "title": "Launch & Optimisation",
      "description": "Your new website goes live! We then continuously monitor performance, providing ongoing support and optimisation to maximise your ROI in Liverpool.",
      "duration": "Week 6+"
    }
  ],
  "featuredTestimonial": {
    "quote": "WebforLeads transformed our online presence. Our new website is not only beautiful but has significantly increased our valuation requests from across Liverpool, especially from areas like the Baltic Triangle and Knowledge Quarter. Truly a game-changer for our agency.",
    "name": "Sarah Jenkins",
    "role": "Director",
    "company": "Mersey Homes Estate Agents",
    "rating": 5,
    "result": "340%",
    "resultLabel": "Increase in Valuation Leads"
  },
  "testimonials": [
    {
      "quote": "Our old site was struggling to attract new vendors. WebforLeads delivered a modern, responsive platform that's now generating consistent enquiries from Penny Lane to Crosby. Exceptional service!",
      "name": "Mark Davies",
      "role": "Owner",
      "company": "Liverpool Property Solutions",
      "industry": "Residential Sales",
      "metric": "315%",
      "metricLabel": "Increase in Enquiries",
      "avatar": "SAW",    },
    {
      "quote": "As a commercial property agent in Liverpool, our website needs to convey professionalism and expertise. WebforLeads understood our niche perfectly, delivering a site that truly reflects our brand and attracts serious investors.",
      "name": "Eleanor Rigby",
      "role": "Senior Partner",
      "company": "Rigby Commercial Properties",
      "industry": "Commercial Real Estate",
      "metric": "200%",
      "metricLabel": "Boost in Site Traffic",
      "avatar": "ER",    },
    {
      "quote": "We needed a website that could handle a large portfolio and integrate with our existing systems. WebforLeads provided a seamless solution, improving our efficiency and lead quality across Merseyside.",
      "name": "David Price",
      "role": "Head of Digital",
      "company": "Wirral Estates Group",
      "industry": "Property Management",
      "metric": "400%",
      "metricLabel": "Reduction in Bounce Rate",
      "avatar": "DP",    }
  ],
  "faqCategories": [
    {
      "category": "Process & Timeline",
      "icon": <Rocket className="h-6 w-6" />,
      "questions": [
        {
          "q": "How long does it take to build a real estate website?",
          "a": "Typically, a bespoke real estate website for a Liverpool agency takes 6-10 weeks from initial concept to launch, depending on complexity and features required."
        },
        {
          "q": "Can you integrate with our existing CRM system?",
          "a": "Yes, we specialise in integrating your new website with popular real estate CRM systems like Alto, Reapit, Jupix, and more, ensuring seamless lead flow and data management."
        },
        {
          "q": "What is the typical process for a project?",
          "a": "Our process involves discovery & strategy, design & development, content & integration, and finally, launch & ongoing optimisation. We keep you informed at every stage."
        }
      ]
    },
    {
      "category": "Services & Features",
      "icon": <Target className="h-6 w-6" />,
      "questions": [
        {
          "q": "Do you include property listing management features?",
          "a": "Absolutely. Our websites come with robust property listing management, allowing you to easily add, edit, and showcase properties with high-quality images, descriptions, and virtual tours."
        },
        {
          "q": "Is the website mobile-friendly?",
          "a": "Yes, all our real estate websites are built with a mobile-first approach, ensuring they look and perform flawlessly on all devices, from smartphones to desktops, crucial for Liverpool's on-the-go market."
        },
        {
          "q": "Can you help with SEO for my real estate website?",
          "a": "Yes, SEO is fundamental to our approach. We build your website with strong SEO foundations and can offer ongoing SEO services to ensure your Liverpool agency ranks highly for key property searches."
        }
      ]
    },
    {
      "category": "Technical & Pricing",
      "icon": <BarChart3 className="h-6 w-6" />,
      "questions": [
        {
          "q": "What is the cost of a real estate agent website?",
          "a": "Our bespoke real estate websites typically range from £3,500 to custom enterprise solutions, depending on the features, integrations, and scale required for your Liverpool agency."
        },
        {
          "q": "Do you offer ongoing support and maintenance?",
          "a": "Yes, we provide comprehensive post-launch support and maintenance packages to ensure your website remains secure, up-to-date, and performing optimally, allowing you to focus on selling properties."
        },
        {
          "q": "What technology do you use to build websites?",
          "a": "We utilise robust, scalable technologies such as WordPress with custom themes, headless CMS solutions, and modern front-end frameworks, ensuring flexibility and performance for your Liverpool property business."
        }
      ]
    }
  ],
  "finalCTA": {
    "badge": "Get Started",
    "title": "Ready to Transform Your Agency's Online Presence?",
    "description": "Partner with WebforLeads to build a powerful, lead-generating website that positions your Liverpool real estate agency for unparalleled success. Let's discuss your vision."
  },
  "structuredData": {
    "serviceName": "Real Estate Agent Website Design Liverpool",
    "description": "WebforLeads offers bespoke, high-converting website design services for real estate agents and property agencies across Liverpool and Merseyside. Specialising in lead generation, property listing integration, and intuitive user experiences.",
    "priceRange": "£3500-Custom",
    "areaServed": "Liverpool",
    "aggregateRating": {
      "ratingValue": "4.9",
      "reviewCount": "127"
    }
  }
};

export const metadata: Metadata = {
  title: serviceConfig.metaTitle,
  description: serviceConfig.metaDescription,
  keywords: serviceConfig.keywords.join(", "),
  openGraph: {
    title: serviceConfig.metaTitle,
    description: serviceConfig.metaDescription,
    type: 'website',
    url: `https://webforleads.uk/web-design/${serviceConfig.serviceSlug}`,
  },
  twitter: {
    card: 'summary_large_image',
    title: serviceConfig.metaTitle,
    description: serviceConfig.metaDescription,
  },
  alternates: {
    canonical: `https://webforleads.uk/web-design/${serviceConfig.serviceSlug}`,
  },
};

export default function RealEstateAgentWebsiteDesignLiverpoolPage() {
  return <ServiceTemplate config={serviceConfig} />;
}


