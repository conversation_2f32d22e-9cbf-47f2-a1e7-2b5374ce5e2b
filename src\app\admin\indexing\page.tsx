"use client";

import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import AdminAuthWrapper from "@/components/admin/admin-auth-wrapper";
import {
  Search, Loader2, RefreshCw, BarChart3, Globe, CheckCircle,
  AlertCircle, Clock, Target, Rocket, FileText, Settings,
  Play, Pause, Square, Eye, ExternalLink, Zap, Activity,
  TrendingUp, Database, Shield, Monitor
} from "lucide-react";

// Animation variants
const fadeInUp = {
  hidden: { opacity: 0, y: 30 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.6,
      ease: [0.21, 1, 0.81, 1] as const
    }
  }
};

const staggerContainer = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: { staggerChildren: 0.1, delayChildren: 0.1 }
  }
};

// Types
interface IndexingStatus {
  quotaUsed: number;
  quotaLimit: number;
  quotaRemaining: number;
  lastResetDate: string;
  isQuotaExceeded: boolean;
}

interface BatchProgress {
  isRunning: boolean;
  currentBatch: number;
  totalBatches: number;
  processedUrls: number;
  totalUrls: number;
  successfulUrls: number;
  failedUrls: number;
  progress: number;
  currentUrl?: string;
  errors: string[];
}

function IndexingAdminPageContent() {
  // State management
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);

  // Status state
  const [indexingStatus, setIndexingStatus] = useState<IndexingStatus | null>(null);
  const [apiAccess, setApiAccess] = useState<any>(null);

  // Single URL submission
  const [singleUrl, setSingleUrl] = useState('');
  const [submittingSingle, setSubmittingSingle] = useState(false);

  // Bulk submission
  const [bulkUrls, setBulkUrls] = useState('');
  const [submittingBulk, setSubmittingBulk] = useState(false);

  // Batch processing state
  const [batchProgress, setBatchProgress] = useState<BatchProgress>({
    isRunning: false,
    currentBatch: 0,
    totalBatches: 0,
    processedUrls: 0,
    totalUrls: 0,
    successfulUrls: 0,
    failedUrls: 0,
    progress: 0,
    errors: []
  });

  // Load initial status
  useEffect(() => {
    loadIndexingStatus();
  }, []);

  const loadIndexingStatus = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/indexing/status');
      const data = await response.json();

      if (data.success) {
        setIndexingStatus(data.quota);
        setApiAccess(data.apiAccess);
        console.log('📊 Indexing status loaded:', data);
      } else {
        setError(`Failed to load status: ${data.error}`);
      }
    } catch (err) {
      setError('Failed to load indexing status');
      console.error('Error loading status:', err);
    } finally {
      setLoading(false);
    }
  };

  const submitSingleUrl = async () => {
    if (!singleUrl.trim()) {
      setError('Please enter a URL');
      return;
    }

    try {
      setSubmittingSingle(true);
      setError(null);
      setSuccess(null);

      console.log('🔄 Submitting single URL:', singleUrl);

      const response = await fetch('/api/indexing/submit-url', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          url: singleUrl,
          type: 'URL_UPDATED'
        }),
      });

      const data = await response.json();
      console.log('📥 Single URL response:', data);

      if (data.success) {
        setSuccess(`✅ URL submitted successfully: ${data.url}`);
        setSingleUrl('');
        // Refresh status
        loadIndexingStatus();
      } else {
        setError(`❌ Failed to submit URL: ${data.error}`);
      }
    } catch (err) {
      setError('Failed to submit URL');
      console.error('Error submitting URL:', err);
    } finally {
      setSubmittingSingle(false);
    }
  };

  const submitBulkUrls = async () => {
    const urls = bulkUrls.split('\n').map(url => url.trim()).filter(url => url);
    
    if (urls.length === 0) {
      setError('Please enter at least one URL');
      return;
    }

    if (urls.length > 50) {
      setError('Maximum 50 URLs per batch');
      return;
    }

    try {
      setSubmittingBulk(true);
      setError(null);
      setSuccess(null);

      console.log('🔄 Submitting bulk URLs:', urls.length);

      const response = await fetch('/api/indexing/bulk-submit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          urls: urls,
          type: 'URL_UPDATED'
        }),
      });

      const data = await response.json();
      console.log('📥 Bulk URLs response:', data);

      if (data.success) {
        setSuccess(`✅ Bulk submission completed: ${data.successfulRequests}/${data.totalRequests} URLs submitted successfully`);
        setBulkUrls('');
        // Refresh status
        loadIndexingStatus();
      } else {
        setError(`❌ Bulk submission failed: ${data.error}`);
      }
    } catch (err) {
      setError('Failed to submit bulk URLs');
      console.error('Error submitting bulk URLs:', err);
    } finally {
      setSubmittingBulk(false);
    }
  };

  const startBatchProcessing = async () => {
    try {
      setBatchProgress(prev => ({ ...prev, isRunning: true, errors: [] }));
      setError(null);
      setSuccess(null);

      console.log('🔄 Starting batch processing of all pages...');

      let startIndex = 0;
      let hasMore = true;
      let totalProcessed = 0;
      let totalSuccessful = 0;
      let totalFailed = 0;
      let batchCount = 0;

      while (hasMore && batchProgress.isRunning) {
        batchCount++;
        
        console.log(`📝 Processing batch ${batchCount}, starting at index ${startIndex}`);

        const response = await fetch('/api/indexing/submit-all-pages', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            startIndex: startIndex,
            batchSize: 50,
            type: 'URL_UPDATED'
          }),
        });

        const data = await response.json();
        console.log(`📥 Batch ${batchCount} response:`, data);

        if (data.success) {
          totalProcessed += data.results.totalRequests;
          totalSuccessful += data.results.successfulRequests;
          totalFailed += data.results.failedRequests;

          setBatchProgress(prev => ({
            ...prev,
            currentBatch: batchCount,
            processedUrls: totalProcessed,
            totalUrls: data.batch.totalUrls,
            successfulUrls: totalSuccessful,
            failedUrls: totalFailed,
            progress: data.batch.progress,
            currentUrl: `Batch ${batchCount} completed`
          }));

          hasMore = data.batch.hasMore;
          startIndex = data.batch.nextStartIndex || 0;

          // Add delay between batches to respect rate limits
          if (hasMore) {
            await new Promise(resolve => setTimeout(resolve, 2000));
          }
        } else {
          setBatchProgress(prev => ({
            ...prev,
            isRunning: false,
            errors: [...prev.errors, `Batch ${batchCount} failed: ${data.error}`]
          }));
          setError(`Batch processing failed at batch ${batchCount}: ${data.error}`);
          break;
        }
      }

      if (!hasMore) {
        setBatchProgress(prev => ({ ...prev, isRunning: false }));
        setSuccess(`🎉 Batch processing completed! ${totalSuccessful} URLs submitted successfully, ${totalFailed} failed.`);
      }

      // Refresh status
      loadIndexingStatus();
    } catch (err) {
      setBatchProgress(prev => ({ ...prev, isRunning: false }));
      setError('Failed to process batch');
      console.error('Error in batch processing:', err);
    }
  };

  const stopBatchProcessing = () => {
    setBatchProgress(prev => ({ ...prev, isRunning: false }));
    setSuccess('Batch processing stopped by user');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-50">
      {/* Header */}
      <section className="py-12 bg-gradient-to-r from-[#1E3A8A] via-[#1E3A8A] to-[#1E40AF]">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial="hidden"
            animate="visible"
            variants={staggerContainer}
            className="text-center"
          >
            <motion.div variants={fadeInUp} className="space-y-6">
              <Badge className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-[#FF6B35]/20 via-white/10 to-white/20 text-white border border-white/20 rounded-full">
                <Search className="h-5 w-5 mr-2 text-[#FF6B35] animate-pulse" />
                <span className="font-bold text-sm tracking-wide">GOOGLE INDEXING API MANAGEMENT</span>
              </Badge>

              <h1 className="text-4xl lg:text-6xl font-black text-white leading-tight">
                Web Search Indexing
                <span className="block text-[#FF6B35]">Admin Dashboard</span>
              </h1>

              <p className="text-xl text-blue-200 max-w-3xl mx-auto leading-relaxed">
                Submit your service area pages to Google's Web Search Indexing API for faster discovery and indexing.
              </p>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Error/Success Messages */}
      {(error || success) && (
        <section className="py-4">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            {error && (
              <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-red-50 border border-red-200 rounded-xl p-4 mb-4"
              >
                <div className="flex items-center">
                  <AlertCircle className="h-5 w-5 text-red-500 mr-3" />
                  <p className="text-red-700 font-medium">{error}</p>
                  <Button
                    onClick={() => setError(null)}
                    variant="ghost"
                    size="sm"
                    className="ml-auto text-red-500 hover:text-red-700"
                  >
                    ×
                  </Button>
                </div>
              </motion.div>
            )}

            {success && (
              <motion.div
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-green-50 border border-green-200 rounded-xl p-4 mb-4"
              >
                <div className="flex items-center">
                  <CheckCircle className="h-5 w-5 text-green-500 mr-3" />
                  <p className="text-green-700 font-medium">{success}</p>
                  <Button
                    onClick={() => setSuccess(null)}
                    variant="ghost"
                    size="sm"
                    className="ml-auto text-green-500 hover:text-green-700"
                  >
                    ×
                  </Button>
                </div>
              </motion.div>
            )}
          </div>
        </section>
      )}

      {/* Status Dashboard */}
      <section className="py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial="hidden"
            animate="visible"
            variants={staggerContainer}
            className="grid grid-cols-1 lg:grid-cols-3 gap-6"
          >
            {/* API Status */}
            <motion.div variants={fadeInUp}>
              <Card className="bg-white border-0 shadow-xl">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-semibold text-[#1E3A8A]">API Status</h3>
                    <Button
                      onClick={loadIndexingStatus}
                      disabled={loading}
                      variant="ghost"
                      size="sm"
                    >
                      {loading ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : (
                        <RefreshCw className="h-4 w-4" />
                      )}
                    </Button>
                  </div>

                  {apiAccess ? (
                    <div className="space-y-3">
                      <div className="flex items-center space-x-2">
                        {apiAccess.success ? (
                          <CheckCircle className="h-5 w-5 text-green-500" />
                        ) : (
                          <AlertCircle className="h-5 w-5 text-red-500" />
                        )}
                        <span className={`font-medium ${apiAccess.success ? 'text-green-700' : 'text-red-700'}`}>
                          {apiAccess.success ? 'API Access: Active' : 'API Access: Failed'}
                        </span>
                      </div>

                      {apiAccess.success && (
                        <div className="text-sm text-gray-600 space-y-1">
                          <div>Project: {apiAccess.details?.projectId}</div>
                          <div>Service Account: {apiAccess.details?.serviceAccount}</div>
                        </div>
                      )}

                      {!apiAccess.success && (
                        <div className="text-sm text-red-600">
                          {apiAccess.error}
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="flex items-center space-x-2">
                      <Loader2 className="h-4 w-4 animate-spin text-gray-400" />
                      <span className="text-gray-500">Loading status...</span>
                    </div>
                  )}
                </CardContent>
              </Card>
            </motion.div>

            {/* Quota Status */}
            <motion.div variants={fadeInUp}>
              <Card className="bg-white border-0 shadow-xl">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-semibold text-[#1E3A8A]">Daily Quota</h3>
                    <BarChart3 className="h-5 w-5 text-[#FF6B35]" />
                  </div>

                  {indexingStatus ? (
                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-600">Used</span>
                        <span className="font-bold text-[#1E3A8A]">{indexingStatus.quotaUsed}</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-600">Limit</span>
                        <span className="font-bold text-gray-700">{indexingStatus.quotaLimit}</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-gray-600">Remaining</span>
                        <span className="font-bold text-green-600">{indexingStatus.quotaRemaining}</span>
                      </div>

                      {/* Progress Bar */}
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-gradient-to-r from-[#1E3A8A] to-[#FF6B35] h-2 rounded-full transition-all duration-500"
                          style={{
                            width: `${Math.min((indexingStatus.quotaUsed / indexingStatus.quotaLimit) * 100, 100)}%`
                          }}
                        ></div>
                      </div>

                      <div className="text-xs text-gray-500">
                        Resets daily at midnight UTC
                      </div>
                    </div>
                  ) : (
                    <div className="flex items-center space-x-2">
                      <Loader2 className="h-4 w-4 animate-spin text-gray-400" />
                      <span className="text-gray-500">Loading quota...</span>
                    </div>
                  )}
                </CardContent>
              </Card>
            </motion.div>

            {/* Quick Stats */}
            <motion.div variants={fadeInUp}>
              <Card className="bg-white border-0 shadow-xl">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-semibold text-[#1E3A8A]">Quick Stats</h3>
                    <Database className="h-5 w-5 text-[#FF6B35]" />
                  </div>

                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600">Total Pages</span>
                      <span className="font-bold text-[#1E3A8A]">1,138</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600">Indexed Today</span>
                      <span className="font-bold text-green-600">{batchProgress.successfulUrls}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600">Failed Today</span>
                      <span className="font-bold text-red-600">{batchProgress.failedUrls}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600">Processing</span>
                      <span className={`font-bold ${batchProgress.isRunning ? 'text-orange-600' : 'text-gray-400'}`}>
                        {batchProgress.isRunning ? 'Active' : 'Idle'}
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Batch Processing Progress */}
      {batchProgress.isRunning && (
        <section className="py-8">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-white rounded-2xl shadow-xl border border-gray-100 p-8"
            >
              <div className="text-center mb-6">
                <div className="flex items-center justify-center space-x-3 mb-4">
                  <Activity className="h-8 w-8 animate-pulse text-[#1E3A8A]" />
                  <h3 className="text-2xl font-bold text-[#1E3A8A]">Batch Processing in Progress</h3>
                </div>
                <p className="text-gray-600">
                  {batchProgress.currentUrl || `Processing batch ${batchProgress.currentBatch}...`}
                </p>
              </div>

              {/* Progress Bar */}
              <div className="mb-6">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm font-medium text-gray-700">
                    Progress: {batchProgress.processedUrls} of {batchProgress.totalUrls} pages
                  </span>
                  <span className="text-sm text-gray-500">
                    {batchProgress.progress}%
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-3">
                  <div
                    className="bg-gradient-to-r from-[#1E3A8A] to-[#FF6B35] h-3 rounded-full transition-all duration-500 ease-out"
                    style={{
                      width: `${batchProgress.progress}%`
                    }}
                  ></div>
                </div>
              </div>

              {/* Stats */}
              <div className="grid grid-cols-4 gap-4 text-center mb-6">
                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="text-2xl font-bold text-[#1E3A8A]">{batchProgress.currentBatch}</div>
                  <div className="text-sm text-gray-600">Current Batch</div>
                </div>
                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="text-2xl font-bold text-green-600">{batchProgress.successfulUrls}</div>
                  <div className="text-sm text-gray-600">Successful</div>
                </div>
                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="text-2xl font-bold text-red-600">{batchProgress.failedUrls}</div>
                  <div className="text-sm text-gray-600">Failed</div>
                </div>
                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="text-2xl font-bold text-[#FF6B35]">{batchProgress.processedUrls}</div>
                  <div className="text-sm text-gray-600">Processed</div>
                </div>
              </div>

              {/* Control Button */}
              <div className="text-center">
                <Button
                  onClick={stopBatchProcessing}
                  variant="outline"
                  className="border-red-200 text-red-600 hover:bg-red-50"
                >
                  <Square className="h-4 w-4 mr-2" />
                  Stop Processing
                </Button>
              </div>

              {/* Errors */}
              {batchProgress.errors.length > 0 && (
                <div className="mt-6 p-4 bg-red-50 rounded-lg">
                  <h4 className="font-medium text-red-800 mb-2">Errors:</h4>
                  <div className="space-y-1">
                    {batchProgress.errors.map((error, index) => (
                      <div key={index} className="text-sm text-red-600">
                        • {error}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </motion.div>
          </div>
        </section>
      )}

      {/* Control Panels */}
      <section className="py-12">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial="hidden"
            animate="visible"
            variants={staggerContainer}
            className="grid grid-cols-1 lg:grid-cols-2 gap-8"
          >
            {/* Single URL Submission */}
            <motion.div variants={fadeInUp}>
              <Card className="bg-white border-0 shadow-xl">
                <CardContent className="p-8">
                  <div className="text-center mb-6">
                    <h2 className="text-2xl font-bold text-[#1E3A8A] mb-2">Submit Single URL</h2>
                    <p className="text-gray-600">Test the API with a single URL submission</p>
                  </div>

                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        URL to Submit
                      </label>
                      <Input
                        type="text"
                        value={singleUrl}
                        onChange={(e) => setSingleUrl(e.target.value)}
                        placeholder="e.g., /web-design-london or https://webforleads.uk/seo-services-manchester"
                        className="w-full"
                      />
                    </div>

                    <Button
                      onClick={submitSingleUrl}
                      disabled={submittingSingle || !singleUrl.trim()}
                      className="w-full bg-gradient-to-r from-[#1E3A8A] to-[#1E3A8A]/90 hover:from-[#1E3A8A]/90 hover:to-[#1E3A8A] text-white py-3 text-lg font-bold rounded-xl"
                    >
                      {submittingSingle ? (
                        <>
                          <Loader2 className="h-5 w-5 mr-2 animate-spin" />
                          Submitting...
                        </>
                      ) : (
                        <>
                          <Zap className="h-5 w-5 mr-2" />
                          Submit URL
                        </>
                      )}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            {/* Bulk URL Submission */}
            <motion.div variants={fadeInUp}>
              <Card className="bg-white border-0 shadow-xl">
                <CardContent className="p-8">
                  <div className="text-center mb-6">
                    <h2 className="text-2xl font-bold text-[#1E3A8A] mb-2">Bulk URL Submission</h2>
                    <p className="text-gray-600">Submit up to 50 URLs at once</p>
                  </div>

                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        URLs (one per line, max 50)
                      </label>
                      <Textarea
                        value={bulkUrls}
                        onChange={(e) => setBulkUrls(e.target.value)}
                        placeholder={`/web-design-london
/seo-services-manchester
/google-ads-birmingham
...`}
                        rows={6}
                        className="w-full"
                      />
                      <p className="text-sm text-gray-500 mt-2">
                        {bulkUrls.split('\n').filter(url => url.trim()).length} URLs
                      </p>
                    </div>

                    <Button
                      onClick={submitBulkUrls}
                      disabled={submittingBulk || !bulkUrls.trim()}
                      className="w-full bg-gradient-to-r from-[#FF6B35] to-[#FF6B35]/90 hover:from-[#FF6B35]/90 hover:to-[#FF6B35] text-white py-3 text-lg font-bold rounded-xl"
                    >
                      {submittingBulk ? (
                        <>
                          <Loader2 className="h-5 w-5 mr-2 animate-spin" />
                          Submitting...
                        </>
                      ) : (
                        <>
                          <Rocket className="h-5 w-5 mr-2" />
                          Submit Bulk URLs
                        </>
                      )}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Batch Processing All Pages */}
      <section className="py-12">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <motion.div
            initial="hidden"
            animate="visible"
            variants={fadeInUp}
          >
            <Card className="bg-gradient-to-br from-slate-50 to-white border-0 shadow-xl">
              <CardContent className="p-8">
                <div className="text-center mb-8">
                  <div className="flex items-center justify-center space-x-3 mb-4">
                    <Globe className="h-8 w-8 text-[#1E3A8A]" />
                    <h2 className="text-3xl font-bold text-[#1E3A8A]">Index All Service Pages</h2>
                  </div>
                  <p className="text-gray-600 max-w-2xl mx-auto">
                    Submit all 1,138 service area pages to Google's indexing API. This process will run in batches
                    of 50 URLs with delays to respect rate limits.
                  </p>
                </div>

                <div className="bg-white rounded-xl p-6 mb-6">
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Batch Processing Details</h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
                    <div className="bg-slate-50 rounded-lg p-4">
                      <div className="text-2xl font-bold text-[#1E3A8A]">1,138</div>
                      <div className="text-sm text-gray-600">Total Pages</div>
                    </div>
                    <div className="bg-slate-50 rounded-lg p-4">
                      <div className="text-2xl font-bold text-[#FF6B35]">~23</div>
                      <div className="text-sm text-gray-600">Batches (50 each)</div>
                    </div>
                    <div className="bg-slate-50 rounded-lg p-4">
                      <div className="text-2xl font-bold text-green-600">~45min</div>
                      <div className="text-sm text-gray-600">Estimated Time</div>
                    </div>
                  </div>
                </div>

                <div className="bg-yellow-50 border border-yellow-200 rounded-xl p-4 mb-6">
                  <div className="flex items-start space-x-3">
                    <AlertCircle className="h-5 w-5 text-yellow-600 mt-0.5" />
                    <div>
                      <h4 className="font-medium text-yellow-800">Important Notes</h4>
                      <ul className="text-sm text-yellow-700 mt-2 space-y-1">
                        <li>• This will use approximately 1,138 of your daily quota (default: 200/day)</li>
                        <li>• Processing includes 2-second delays between batches</li>
                        <li>• You can stop the process at any time</li>
                        <li>• Failed submissions will be logged for retry</li>
                      </ul>
                    </div>
                  </div>
                </div>

                <div className="text-center">
                  <Button
                    onClick={startBatchProcessing}
                    disabled={batchProgress.isRunning || (indexingStatus?.quotaRemaining || 0) < 50}
                    className="bg-gradient-to-r from-[#1E3A8A] via-[#1E3A8A] to-[#1E40AF] hover:from-[#1E3A8A]/90 hover:to-[#1E40AF]/90 text-white px-8 py-4 text-lg font-bold rounded-xl shadow-lg"
                  >
                    {batchProgress.isRunning ? (
                      <>
                        <Loader2 className="h-6 w-6 mr-3 animate-spin" />
                        Processing Batch {batchProgress.currentBatch}...
                      </>
                    ) : (
                      <>
                        <Play className="h-6 w-6 mr-3" />
                        Start Batch Processing
                      </>
                    )}
                  </Button>

                  {(indexingStatus?.quotaRemaining || 0) < 50 && (
                    <p className="text-sm text-red-600 mt-2">
                      Insufficient quota remaining. Need at least 50 requests to start batch processing.
                    </p>
                  )}
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </section>

      {/* Footer Info */}
      <section className="py-8 bg-slate-50">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="flex items-center justify-center space-x-6 text-sm text-gray-600">
            <div className="flex items-center space-x-2">
              <Shield className="h-4 w-4" />
              <span>Secure API Connection</span>
            </div>
            <div className="flex items-center space-x-2">
              <Monitor className="h-4 w-4" />
              <span>Real-time Progress</span>
            </div>
            <div className="flex items-center space-x-2">
              <TrendingUp className="h-4 w-4" />
              <span>Rate Limited</span>
            </div>
          </div>
          <p className="text-xs text-gray-500 mt-2">
            Google Web Search Indexing API • Project: myagency-468706 • webforleads.uk
          </p>
        </div>
      </section>
    </div>
  );
}

export default function IndexingAdminPage() {
  return (
    <AdminAuthWrapper title="Google Indexing Management">
      <IndexingAdminPageContent />
    </AdminAuthWrapper>
  );
}
