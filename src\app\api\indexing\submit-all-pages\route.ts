import { NextRequest, NextResponse } from 'next/server';
import { googleIndexingService } from '@/lib/google-indexing';
import fs from 'fs';
import path from 'path';

// Get all registered routes from route registry
function getAllRegisteredRoutes(): string[] {
  try {
    const routeRegistryDir = path.join(process.cwd(), 'route-registry');
    const urls: string[] = [];

    if (fs.existsSync(routeRegistryDir)) {
      const files = fs.readdirSync(routeRegistryDir);
      
      for (const file of files) {
        if (file.startsWith('route-') && file.endsWith('.json')) {
          const filepath = path.join(routeRegistryDir, file);
          const content = fs.readFileSync(filepath, 'utf-8');
          const route = JSON.parse(content);
          
          // Build URL from slug
          const url = `/${route.slug}`;
          urls.push(url);
        }
      }
    }

    console.log(`📊 Found ${urls.length} registered routes`);
    return urls;
  } catch (error) {
    console.error('❌ Error reading route registry:', error);
    return [];
  }
}

export async function POST(request: NextRequest) {
  try {
    console.log('🔄 Starting submission of all pages...');
    
    const body = await request.json();
    const { batchSize = 50, type = 'URL_UPDATED', startIndex = 0 } = body;

    // Get all registered routes
    const allUrls = getAllRegisteredRoutes();
    
    if (allUrls.length === 0) {
      console.log('❌ No registered routes found');
      return NextResponse.json(
        { success: false, error: 'No registered routes found' },
        { status: 404 }
      );
    }

    // Calculate batch
    const endIndex = Math.min(startIndex + batchSize, allUrls.length);
    const batchUrls = allUrls.slice(startIndex, endIndex);
    
    console.log(`📝 Processing batch ${startIndex + 1}-${endIndex} of ${allUrls.length} total URLs`);
    console.log(`📝 Batch size: ${batchUrls.length}`);
    console.log(`📝 Type: ${type}`);

    // Submit batch to Google Indexing API
    const result = await googleIndexingService.submitBatch(batchUrls, type);

    const hasMore = endIndex < allUrls.length;
    const nextStartIndex = hasMore ? endIndex : null;

    console.log(`📊 Batch submission completed:`);
    console.log(`   Processed: ${endIndex}/${allUrls.length}`);
    console.log(`   Successful: ${result.successfulRequests}`);
    console.log(`   Failed: ${result.failedRequests}`);
    console.log(`   Has more: ${hasMore}`);

    return NextResponse.json({
      success: result.success,
      message: `Batch ${startIndex + 1}-${endIndex} completed. ${result.successfulRequests}/${result.totalRequests} URLs submitted successfully.`,
      batch: {
        startIndex,
        endIndex,
        batchSize: batchUrls.length,
        totalUrls: allUrls.length,
        hasMore,
        nextStartIndex,
        progress: Math.round((endIndex / allUrls.length) * 100),
      },
      results: {
        totalRequests: result.totalRequests,
        successfulRequests: result.successfulRequests,
        failedRequests: result.failedRequests,
        quotaUsed: result.quotaUsed,
      },
      details: result.results,
    });
  } catch (error: any) {
    console.error('❌ Unexpected error in submit-all-pages API:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        details: error.message,
      },
      { status: 500 }
    );
  }
}
