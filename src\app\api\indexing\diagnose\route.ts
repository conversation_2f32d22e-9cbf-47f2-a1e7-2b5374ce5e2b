import { NextRequest, NextResponse } from 'next/server';
import { google } from 'googleapis';

export async function GET(request: NextRequest) {
  try {
    console.log('🔍 Starting Google Indexing API diagnostics...');
    
    const diagnostics = {
      timestamp: new Date().toISOString(),
      environment: {},
      serviceAccount: {},
      apiAccess: {},
      recommendations: []
    };

    // Check environment variables
    console.log('📋 Checking environment variables...');
    const serviceAccountKeyString = process.env.GOOGLE_INDEXING_SERVICE_ACCOUNT_KEY;
    const projectId = process.env.GOOGLE_INDEXING_PROJECT_ID;
    const siteUrl = process.env.NEXT_PUBLIC_SITE_URL;

    diagnostics.environment = {
      hasServiceAccountKey: !!serviceAccountKeyString,
      hasProjectId: !!projectId,
      hasSiteUrl: !!siteUrl,
      siteUrl: siteUrl || 'Not set',
      projectId: projectId || 'Not set'
    };

    if (!serviceAccountKeyString) {
      diagnostics.recommendations.push('❌ GOOGLE_INDEXING_SERVICE_ACCOUNT_KEY environment variable is missing');
      return NextResponse.json(diagnostics);
    }

    // Parse service account key
    console.log('🔑 Parsing service account key...');
    let serviceAccountKey;
    try {
      serviceAccountKey = JSON.parse(serviceAccountKeyString);
      diagnostics.serviceAccount = {
        isValidJson: true,
        projectId: serviceAccountKey.project_id,
        clientEmail: serviceAccountKey.client_email,
        hasPrivateKey: !!serviceAccountKey.private_key,
        privateKeyLength: serviceAccountKey.private_key?.length || 0
      };
    } catch (error) {
      diagnostics.serviceAccount = {
        isValidJson: false,
        error: 'Failed to parse service account JSON'
      };
      diagnostics.recommendations.push('❌ Service account key is not valid JSON');
      return NextResponse.json(diagnostics);
    }

    // Test JWT client creation
    console.log('🔐 Testing JWT client creation...');
    try {
      // Ensure private key has proper line breaks
      const privateKey = serviceAccountKey.private_key.replace(/\\n/g, '\n');

      const jwtClient = new google.auth.JWT({
        email: serviceAccountKey.client_email,
        key: privateKey,
        scopes: ['https://www.googleapis.com/auth/indexing'],
      });

      diagnostics.apiAccess.jwtClientCreated = true;
      
      // Test authentication
      console.log('🔓 Testing authentication...');
      await jwtClient.authorize();
      diagnostics.apiAccess.authenticationSuccessful = true;
      
      // Test API initialization
      console.log('🔧 Testing API initialization...');
      const indexing = google.indexing({
        version: 'v3',
        auth: jwtClient,
      });
      
      diagnostics.apiAccess.apiInitialized = true;
      
      // Test a simple API call (this will likely fail due to domain verification)
      console.log('📡 Testing API call...');
      try {
        const testUrl = siteUrl || 'https://webforleads.uk';
        const response = await indexing.urlNotifications.publish({
          requestBody: {
            url: testUrl,
            type: 'URL_UPDATED',
          },
        });
        
        diagnostics.apiAccess.apiCallSuccessful = true;
        diagnostics.apiAccess.testResponse = response.data;
        diagnostics.recommendations.push('✅ API is working correctly!');
        
      } catch (apiError: any) {
        diagnostics.apiAccess.apiCallSuccessful = false;
        diagnostics.apiAccess.apiError = {
          message: apiError.message,
          code: apiError.code,
          status: apiError.status
        };
        
        if (apiError.message?.includes('authentication credential')) {
          diagnostics.recommendations.push('❌ Domain not verified in Google Search Console');
          diagnostics.recommendations.push('📝 Steps to fix:');
          diagnostics.recommendations.push('   1. Go to https://search.google.com/search-console');
          diagnostics.recommendations.push(`   2. Add property: ${siteUrl || 'webforleads.uk'}`);
          diagnostics.recommendations.push('   3. Verify domain ownership');
          diagnostics.recommendations.push(`   4. Add service account as user: ${serviceAccountKey.client_email}`);
          diagnostics.recommendations.push('   5. Set permission to "Owner" or "Full"');
        } else if (apiError.message?.includes('quota')) {
          diagnostics.recommendations.push('❌ API quota exceeded');
        } else {
          diagnostics.recommendations.push(`❌ API error: ${apiError.message}`);
        }
      }
      
    } catch (authError: any) {
      diagnostics.apiAccess.jwtClientCreated = false;
      diagnostics.apiAccess.authError = authError.message;
      diagnostics.recommendations.push('❌ JWT authentication failed');
      diagnostics.recommendations.push('🔍 Check service account key validity');
    }

    // Additional checks
    console.log('🔍 Running additional checks...');
    
    // Check if API is enabled
    diagnostics.recommendations.push('📋 Verify these settings:');
    diagnostics.recommendations.push('   • Web Search Indexing API is enabled in Google Cloud Console');
    diagnostics.recommendations.push('   • Service account has proper permissions');
    diagnostics.recommendations.push('   • Domain is verified in Google Search Console');
    diagnostics.recommendations.push(`   • Service account (${serviceAccountKey.client_email}) is added as user in Search Console`);

    console.log('✅ Diagnostics completed');
    
    return NextResponse.json({
      success: true,
      diagnostics,
      summary: {
        environmentOk: diagnostics.environment.hasServiceAccountKey && diagnostics.environment.hasSiteUrl,
        serviceAccountOk: diagnostics.serviceAccount.isValidJson,
        authenticationOk: diagnostics.apiAccess.authenticationSuccessful,
        apiCallOk: diagnostics.apiAccess.apiCallSuccessful
      }
    });

  } catch (error: any) {
    console.error('❌ Diagnostics failed:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Diagnostics failed',
        details: error.message,
      },
      { status: 500 }
    );
  }
}
