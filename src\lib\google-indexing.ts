import { google } from 'googleapis';

// Types for indexing operations
export interface IndexingRequest {
  url: string;
  type: 'URL_UPDATED' | 'URL_DELETED';
}

export interface IndexingResponse {
  success: boolean;
  url: string;
  status?: string;
  error?: string;
  details?: any;
}

export interface IndexingBatchResponse {
  success: boolean;
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  results: IndexingResponse[];
  quotaUsed: number;
  quotaRemaining?: number;
}

export interface IndexingStatus {
  quotaUsed: number;
  quotaLimit: number;
  quotaRemaining: number;
  lastResetDate: string;
  isQuotaExceeded: boolean;
}

// Google Indexing Service Class
export class GoogleIndexingService {
  private indexing: any;
  private serviceAccountKey: any;
  private projectId: string;
  private baseUrl: string;

  constructor() {
    // Parse service account key from environment
    const serviceAccountKeyString = process.env.GOOGLE_INDEXING_SERVICE_ACCOUNT_KEY;
    if (!serviceAccountKeyString) {
      throw new Error('GOOGLE_INDEXING_SERVICE_ACCOUNT_KEY environment variable is required');
    }

    try {
      this.serviceAccountKey = JSON.parse(serviceAccountKeyString);
    } catch (error) {
      throw new Error('Invalid GOOGLE_INDEXING_SERVICE_ACCOUNT_KEY format');
    }

    this.projectId = this.serviceAccountKey.project_id;
    this.baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://webforleads.uk';

    // Initialize Google APIs client
    this.initializeClient();
  }

  private initializeClient() {
    try {
      // Create JWT client for authentication
      const jwtClient = new google.auth.JWT(
        this.serviceAccountKey.client_email,
        undefined,
        this.serviceAccountKey.private_key,
        ['https://www.googleapis.com/auth/indexing'],
        undefined
      );

      // Initialize the indexing API
      this.indexing = google.indexing({
        version: 'v3',
        auth: jwtClient,
      });

      console.log('✅ Google Indexing API client initialized successfully');
    } catch (error) {
      console.error('❌ Failed to initialize Google Indexing API client:', error);
      throw error;
    }
  }

  // Submit a single URL for indexing
  async submitUrl(url: string, type: 'URL_UPDATED' | 'URL_DELETED' = 'URL_UPDATED'): Promise<IndexingResponse> {
    try {
      console.log(`🔄 Submitting URL for indexing: ${url}`);
      console.log(`📝 Request type: ${type}`);

      // Ensure URL is absolute
      const fullUrl = url.startsWith('http') ? url : `${this.baseUrl}${url}`;
      
      const requestBody = {
        url: fullUrl,
        type: type,
      };

      console.log('📤 Request body:', JSON.stringify(requestBody, null, 2));

      const response = await this.indexing.urlNotifications.publish({
        requestBody: requestBody,
      });

      console.log('📥 API Response:', JSON.stringify(response.data, null, 2));

      return {
        success: true,
        url: fullUrl,
        status: 'submitted',
        details: response.data,
      };
    } catch (error: any) {
      console.error(`❌ Failed to submit URL ${url}:`, error);
      
      let errorMessage = 'Unknown error';
      if (error.response?.data?.error) {
        errorMessage = error.response.data.error.message || error.response.data.error;
      } else if (error.message) {
        errorMessage = error.message;
      }

      return {
        success: false,
        url: url,
        error: errorMessage,
        details: error.response?.data || error,
      };
    }
  }

  // Submit multiple URLs in batch
  async submitBatch(urls: string[], type: 'URL_UPDATED' | 'URL_DELETED' = 'URL_UPDATED'): Promise<IndexingBatchResponse> {
    console.log(`🔄 Starting batch submission of ${urls.length} URLs`);
    
    const results: IndexingResponse[] = [];
    let successfulRequests = 0;
    let failedRequests = 0;

    for (let i = 0; i < urls.length; i++) {
      const url = urls[i];
      console.log(`📝 Processing URL ${i + 1}/${urls.length}: ${url}`);
      
      try {
        const result = await this.submitUrl(url, type);
        results.push(result);
        
        if (result.success) {
          successfulRequests++;
          console.log(`✅ Successfully submitted: ${url}`);
        } else {
          failedRequests++;
          console.log(`❌ Failed to submit: ${url} - ${result.error}`);
        }
        
        // Add delay between requests to avoid rate limiting
        if (i < urls.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 1000)); // 1 second delay
        }
      } catch (error) {
        console.error(`❌ Unexpected error processing ${url}:`, error);
        results.push({
          success: false,
          url: url,
          error: 'Unexpected error during submission',
        });
        failedRequests++;
      }
    }

    console.log(`📊 Batch submission completed:`);
    console.log(`   Total: ${urls.length}`);
    console.log(`   Successful: ${successfulRequests}`);
    console.log(`   Failed: ${failedRequests}`);

    return {
      success: failedRequests === 0,
      totalRequests: urls.length,
      successfulRequests,
      failedRequests,
      results,
      quotaUsed: urls.length, // Each request uses 1 quota
    };
  }

  // Get indexing status for a URL
  async getUrlStatus(url: string): Promise<any> {
    try {
      const fullUrl = url.startsWith('http') ? url : `${this.baseUrl}${url}`;
      
      const response = await this.indexing.urlNotifications.getMetadata({
        url: fullUrl,
      });

      return {
        success: true,
        url: fullUrl,
        metadata: response.data,
      };
    } catch (error: any) {
      console.error(`❌ Failed to get status for URL ${url}:`, error);
      return {
        success: false,
        url: url,
        error: error.message || 'Failed to get URL status',
      };
    }
  }

  // Validate service account and API access
  async validateAccess(): Promise<{ success: boolean; error?: string; details?: any }> {
    try {
      console.log('🔍 Validating Google Indexing API access...');
      
      // Try to submit a test URL (your homepage)
      const testUrl = this.baseUrl;
      const result = await this.submitUrl(testUrl, 'URL_UPDATED');
      
      if (result.success) {
        console.log('✅ API access validation successful');
        return {
          success: true,
          details: {
            projectId: this.projectId,
            serviceAccount: this.serviceAccountKey.client_email,
            testUrl: testUrl,
            result: result,
          },
        };
      } else {
        console.log('❌ API access validation failed');
        return {
          success: false,
          error: result.error,
          details: result.details,
        };
      }
    } catch (error: any) {
      console.error('❌ API access validation error:', error);
      return {
        success: false,
        error: error.message || 'Validation failed',
        details: error,
      };
    }
  }

  // Get current quota usage (estimated)
  getQuotaInfo(): IndexingStatus {
    // Note: Google doesn't provide real-time quota info via API
    // This is an estimated implementation
    const now = new Date();
    const todayStart = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    
    return {
      quotaUsed: 0, // Would need to track this in your database
      quotaLimit: 200, // Default quota limit
      quotaRemaining: 200,
      lastResetDate: todayStart.toISOString(),
      isQuotaExceeded: false,
    };
  }
}

// Export singleton instance
export const googleIndexingService = new GoogleIndexingService();
