import { Metadata } from 'next';
import ServiceTemplate from '../../site-pages/service-template';
import {
  Palette, Smartphone, TrendingUp, Search, Brain, Gauge,
  Target, Users, Rocket, BarChart3, Globe, Zap, Code,
  DollarSign, MousePointer, Share2, Heart, MessageCircle,
  Camera, Tablet, Monitor
} from "lucide-react";

// Web Design Services Service Configuration for web-design-services-liverpool
// Generated using Web Design Services template for WebforLeads
const serviceConfig = {
  "serviceName": "Web Design Services",
  "serviceSlug": "web-design-services-liverpool",
  "metaTitle": "Web Design Liverpool | Bespoke Websites for Growth",
  "metaDescription": "Elevate your Liverpool business with conversion-focused web design services from WebforLeads. Bespoke, responsive websites built for impact and ROI.",
  "keywords": [
    "web design liverpool",
    "liverpool web design",
    "website design liverpool",
    "bespoke web design uk",
    "responsive web design",
    "ecommerce web design liverpool",
    "web development liverpool",  ],
  "heroTitle": "Bespoke Web Design Services in Liverpool",
  "heroSubtitle": "Crafting High-Performance Websites for Liverpool Businesses",
  "heroDescription": "WebforLeads delivers cutting-edge web design solutions tailored for the vibrant Liverpool market. From the Baltic Triangle to the Knowledge Quarter, we build stunning, conversion-optimised websites that drive real growth for your business.",
  "heroBadgeText": "Liverpool's Trusted Web Experts",
  "stats": [
    {
      "number": "500%",
      "label": "ROI for Clients",
      "icon": <TrendingUp className="h-6 w-6" />
    },
    {
      "number": "£1.8M+",
      "label": "Generated Revenue",
      "icon": <DollarSign className="h-6 w-6" />
    },
    {
      "number": "96%",
      "label": "Client Satisfaction",
      "icon": <Heart className="h-6 w-6" />
    },
    {
      "number": "24hrs",
      "label": "Support Response",
      "icon": <Zap className="h-6 w-6" />
    }
  ],
  "designFeatures": [
    {
      "icon": <Brain className="h-8 w-8" />,
      "title": "Strategic UX/UI",
      "description": "Intuitive user experiences and compelling interfaces designed to engage your Liverpool audience and guide them towards conversion.",
      "highlight": "User-Centric Design"
    },
    {
      "icon": <Gauge className="h-8 w-8" />,
      "title": "Performance Optimisation",
      "description": "Lightning-fast loading speeds and seamless functionality ensure your website ranks higher and retains visitors, crucial for businesses across Merseyside.",
      "highlight": "Blazing Fast"
    },
    {
      "icon": <Target className="h-6 w-6" />,
      "title": "Conversion-Driven Layouts",
      "description": "Every element is strategically placed to maximise leads and sales, turning visitors into loyal customers for your Liverpool enterprise.",
      "highlight": "Maximise ROI"
    },
    {
      "icon": <BarChart3 className="h-6 w-6" />,
      "title": "SEO-Ready Foundations",
      "description": "Built from the ground up with search engine best practices, ensuring your Liverpool business is easily found by potential clients on Google.",
      "highlight": "Organic Visibility"
    }
  ],
  "features": [
    {
      "icon": <Search className="h-6 w-6" />,
      "title": "SEO Integration",
      "description": "We build websites with search engine visibility in mind, ensuring your Liverpool business ranks highly for relevant local searches."
    },
    {
      "icon": <Target className="h-6 w-6" />,
      "title": "Conversion Rate Optimisation",
      "description": "Our designs are meticulously crafted to convert visitors into leads and customers, driving tangible results for your business in areas like the Commercial District."
    },
    {
      "icon": <Globe className="h-6 w-6" />,
      "title": "Responsive Design",
      "description": "Your website will look and perform flawlessly on any device, from desktops to mobiles, ensuring a consistent experience for users across Liverpool and beyond."
    },
    {
      "icon": <Zap className="h-6 w-6" />,
      "title": "Fast Loading Speeds",
      "description": "Optimised for speed, our websites provide an excellent user experience, reducing bounce rates and improving search rankings."
    },
    {
      "icon": <BarChart3 className="h-6 w-6" />,
      "title": "Analytics & Reporting",
      "description": "Gain clear insights into your website's performance with comprehensive analytics, empowering data-driven decisions for your Liverpool business."
    },
    {
      "icon": <Users className="h-6 w-6" />,
      "title": "User-Friendly CMS",
      "description": "Easily manage your content with an intuitive Content Management System, giving you full control over your site's updates."
    }
  ],
  "packages": [
    {
      "name": "Starter Website",
      "price": "£3,500",
      "period": "one-time",
      "description": "Ideal for new businesses or those needing a professional online presence quickly.",
      "features": [
        "Responsive Design",
        "Up to 5 Pages",
        "Basic SEO Setup",
        "Contact Form Integration",      ],
      "highlight": false,
      "cta": "Get Started"
    },
    {
      "name": "Growth Website",
      "price": "£7,500",
      "period": "one-time",
      "description": "Perfect for established Liverpool businesses seeking to expand their digital footprint and generate more leads.",
      "features": [
        "Everything in Starter",
        "Up to 15 Pages",
        "Advanced SEO Optimisation",
        "CRM Integration",
        "Blog/News Section",      ],
      "highlight": true,
      "cta": "Boost My Business"
    },
    {
      "name": "Enterprise Solutions",
      "price": "Custom",
      "period": "one-time",
      "description": "Tailored web solutions for complex requirements, large-scale projects, or bespoke functionalities.",
      "features": [
        "Fully Bespoke Design",
        "Unlimited Pages",
        "Custom Integrations (APIs, ERPs)",
        "E-commerce Functionality",
        "Ongoing CRO & A/B Testing",      ],
      "highlight": false,
      "cta": "Request a Quote"
    }
  ],
  "process": [
    {
      "step": "01",
      "title": "Discovery & Strategy",
      "description": "We begin by understanding your Liverpool business, target audience, and goals to craft a bespoke digital strategy.",
      "duration": "Week 1"
    },
    {
      "step": "02",
      "title": "Design & Prototyping",
      "description": "Our designers create stunning, user-centric wireframes and mock-ups, ensuring your vision for your Liverpool site comes to life.",
      "duration": "Week 2-3"
    },
    {
      "step": "03",
      "title": "Development & Testing",
      "description": "Our developers build your website with clean code, ensuring responsiveness, speed, and robust functionality across all devices.",
      "duration": "Week 4-5"
    },
    {
      "step": "04",
      "title": "Launch & Optimisation",
      "description": "After rigorous testing, your new website goes live. We then monitor performance and provide ongoing optimisation to ensure continued success.",
      "duration": "Week 6+"
    }
  ],
  "featuredTestimonial": {
    "quote": "WebforLeads transformed our online presence. Our new website is not only stunning but has significantly boosted our enquiries from the Liverpool area. Truly exceptional service!",
    "name": "Sarah Jenkins",
    "role": "Director",
    "company": "Mersey Property Group",
    "rating": 5,
    "result": "280%",
    "resultLabel": "Increase in Enquiries"
  },
  "testimonials": [
    {
      "quote": "Our new site by WebforLeads has been a game-changer for our retail business in Bold Street. Professional, efficient, and delivered real results.",
      "name": "Mark Davies",
      "role": "Owner",
      "company": "Liverpool Vintage Finds",
      "industry": "Retail",
      "metric": "315%",
      "metricLabel": "Online Sales Growth",
      "avatar": "SW",    },
    {
      "quote": "WebforLeads understood our vision perfectly. Our new website has elevated our brand and attracted more clients from the business districts around Dale Street.",
      "name": "Emma Roberts",
      "role": "Marketing Manager",
      "company": "Waterfront Legal Services",
      "industry": "Legal",
      "metric": "200%",
      "metricLabel": "Website Traffic Increase",
      "avatar": "ER",    },
    {
      "quote": "The team at WebforLeads delivered a fantastic, user-friendly website that truly captures our brand. We've seen a significant uplift in bookings from across Merseyside.",
      "name": "David Price",
      "role": "Operations Director",
      "company": "Albert Dock Tours",
      "industry": "Tourism",
      "metric": "400%",
      "metricLabel": "Booking Conversions",
      "avatar": "DP",    }
  ],
  "faqCategories": [
    {
      "category": "Process & Timeline",
      "icon": <Rocket className="h-6 w-6" />,
      "questions": [
        {
          "q": "How long does a typical web design project take?",
          "a": "Most bespoke web design projects for Liverpool businesses are completed within 6-10 weeks, depending on complexity and client feedback."
        },
        {
          "q": "What is your design process?",
          "a": "Our process involves discovery, strategy, UX/UI design, development, rigorous testing, launch, and ongoing optimisation to ensure your Liverpool site performs."
        },
        {
          "q": "Can I see progress during the design phase?",
          "a": "Absolutely. We provide regular updates and access to staging environments so you can review and provide feedback at key milestones."
        }
      ]
    },
    {
      "category": "Services & Features",
      "icon": <Target className="h-6 w-6" />,
      "questions": [
        {
          "q": "Do you offer e-commerce web design for Liverpool businesses?",
          "a": "Yes, we specialise in creating robust, secure, and conversion-focused e-commerce websites for retail businesses across Liverpool and the North West."
        },
        {
          "q": "Is my website mobile-friendly?",
          "a": "All websites designed by WebforLeads are fully responsive, ensuring they look and function perfectly on all devices, from smartphones to desktops."
        },
        {
          "q": "Will my website be SEO-optimised?",
          "a": "Yes, SEO best practices are integrated from the ground up, helping your Liverpool business rank higher in search results and attract local customers."
        }
      ]
    },
    {
      "category": "Technical & Pricing",
      "icon": <BarChart3 className="h-6 w-6" />,
      "questions": [
        {
          "q": "What is the cost of web design services in Liverpool?",
          "a": "Our bespoke web design projects typically start from £3,500, with pricing varying based on complexity, features, and specific business requirements."
        },
        {
          "q": "Do you provide website maintenance after launch?",
          "a": "Yes, we offer comprehensive maintenance and support packages to ensure your website remains secure, updated, and performing optimally post-launch."
        },
        {
          "q": "What CMS do you use?",
          "a": "We primarily work with WordPress for its flexibility and user-friendliness, but we can also develop on other platforms based on your specific needs."
        }
      ]
    }
  ],
  "finalCTA": {
    "badge": "Ready for Growth?",
    "title": "Transform Your Liverpool Business Online",
    "description": "Partner with WebforLeads to create a powerful, conversion-driven website that stands out in the competitive Liverpool market. Let's build your digital success story."
  },
  "structuredData": {
    "serviceName": "Web Design Services Liverpool",
    "description": "WebforLeads offers bespoke, conversion-focused web design services for businesses in Liverpool and surrounding areas, driving online growth and ROI.",
    "priceRange": "£3500-£15000+",
    "areaServed": "Liverpool",
    "aggregateRating": {
      "ratingValue": "4.9",
      "reviewCount": "127"
    }
  }
};

export const metadata: Metadata = {
  title: serviceConfig.metaTitle,
  description: serviceConfig.metaDescription,
  keywords: serviceConfig.keywords.join(", "),
  openGraph: {
    title: serviceConfig.metaTitle,
    description: serviceConfig.metaDescription,
    type: 'website',
    url: `https://webforleads.uk/web-design/${serviceConfig.serviceSlug}`,
  },
  twitter: {
    card: 'summary_large_image',
    title: serviceConfig.metaTitle,
    description: serviceConfig.metaDescription,
  },
  alternates: {
    canonical: `https://webforleads.uk/web-design/${serviceConfig.serviceSlug}`,
  },
};

export default function WebDesignServicesLiverpoolPage() {
  return <ServiceTemplate config={serviceConfig} />;
}


