import { Metadata } from 'next';
import ServiceTemplate from '../../site-pages/service-template';
import {
  Palette, Smartphone, TrendingUp, Search, Brain, Gauge,
  Target, Users, Rocket, BarChart3, Globe, Zap, Code,
  DollarSign, MousePointer, Share2, Heart, MessageCircle,
  Camera, Tablet, Monitor
} from "lucide-react";

// Salon and Spa Website Design Service Configuration for salon-and-spa-website-design-bristol
// Generated using Web Design Services template for WebforLeads
const serviceConfig = {
  "serviceName": "Salon and Spa Website Design",
  "serviceSlug": "salon-and-spa-website-design-bristol",
  "metaTitle": "Salon & Spa Website Design Bristol | WebforLeads UK",
  "metaDescription": "Elevate your Bristol salon or spa with a stunning, conversion-focused website. WebforLeads crafts bespoke digital experiences that attract and retain clients.",
  "keywords": [
    "salon website design bristol",
    "spa website design bristol",
    "hair salon web design",
    "beauty salon website",
    "bristol web design for salons",
    "spa marketing bristol",
    "web design bristol",  ],
  "heroTitle": "Bespoke Website Design for Bristol's Premier Salons & Spas",
  "heroSubtitle": "Transforming your digital presence into a client magnet across Bristol and the South West.",
  "heroDescription": "At WebforLeads, we understand the unique allure of Bristol's beauty and wellness sector. From the bustling streets of Clifton Village to the vibrant independent businesses of Gloucester Road, we craft stunning, high-performing websites that capture your brand's essence and drive bookings. Our designs are not just beautiful; they're built for conversion, ensuring your salon or spa stands out in a competitive market.",
  "heroBadgeText": "BRISTOL'S DIGITAL EXPERTS",
  "stats": [
    {
      "number": "340%",
      "label": "Average ROI for clients",
      "icon": <TrendingUp className="h-6 w-6" />
    },
    {
      "number": "£1.2M+",
      "label": "Client revenue generated",
      "icon": <DollarSign className="h-6 w-6" />
    },
    {
      "number": "98%",
      "label": "Client satisfaction rate",
      "icon": <Heart className="h-6 w-6" />
    },
    {
      "number": "2019",
      "label": "Established in Bristol",
      "icon": <Rocket className="h-6 w-6" />
    }
  ],
  "designFeatures": [
    {
      "icon": <Smartphone className="h-6 w-6" />,
      "title": "Mobile-First Responsiveness",
      "description": "Seamless experiences on any device, from Clifton to Portishead, ensuring your site looks perfect everywhere.",
      "highlight": "Optimised for all screens"
    },
    {
      "icon": <Palette className="h-6 w-6" />,
      "title": "Brand-Centric Aesthetics",
      "description": "Designs that reflect your unique salon or spa identity, captivating your Bristol clientele from the first click.",
      "highlight": "Your brand, amplified"
    },
    {
      "icon": <MousePointer className="h-6 w-6" />,
      "title": "Intuitive User Journeys",
      "description": "Effortless navigation and booking flows, converting website visitors into loyal clients with ease.",
      "highlight": "Convert more visitors"
    },
    {
      "icon": <BarChart3 className="h-6 w-6" />,
      "title": "SEO & Conversion Optimisation",
      "description": "Built from the ground up to rank higher in Bristol searches and maximise your online bookings.",
      "highlight": "Rank higher, book more"
    }
  ],
  "features": [
    {
      "icon": <Search className="h-6 w-6" />,
      "title": "Local SEO Dominance",
      "description": "Ensuring your salon or spa is found by clients searching in Bristol, Bath, and surrounding areas."
    },
    {
      "icon": <Target className="h-6 w-6" />,
      "title": "Integrated Booking Systems",
      "description": "Seamless integration with your preferred booking software for effortless client scheduling and management."
    },
    {
      "icon": <Globe className="h-6 w-6" />,
      "title": "Stunning Portfolio Galleries",
      "description": "Showcase your best work with high-resolution images and videos that impress and inspire potential clients."
    },
    {
      "icon": <Zap className="h-6 w-6" />,
      "title": "Blazing Fast Load Times",
      "description": "Optimised performance to keep visitors engaged and reduce bounce rates, improving user experience."
    },
    {
      "icon": <BarChart3 className="h-6 w-6" />,
      "title": "Analytics & Reporting",
      "description": "Gain insights into your website's performance with comprehensive data dashboards to inform your strategy."
    },
    {
      "icon": <Users className="h-6 w-6" />,
      "title": "Client Testimonial Integration",
      "description": "Build trust and credibility by showcasing glowing reviews from your satisfied customers directly on your site."
    }
  ],
  "packages": [
    {
      "name": "Essential Presence",
      "price": "£3,500",
      "period": "one-time",
      "description": "Ideal for new salons or spas in Bristol seeking a professional, high-converting online presence to get started.",
      "features": [
        "Custom Design (5 pages)",
        "Mobile Responsive",
        "Basic SEO Setup",
        "Booking System Integration",      ],
      "highlight": false,
      "cta": "Get Started"
    },
    {
      "name": "Growth Accelerator",
      "price": "£7,500",
      "period": "one-time",
      "description": "Our most popular package for established Bristol salons ready to dominate their local market and expand their reach.",
      "features": [
        "Premium Custom Design (10 pages)",
        "Advanced SEO Strategy",
        "CRM Integration",
        "Content Creation Support",
        "Enhanced Security",      ],
      "highlight": true,
      "cta": "Boost My Bookings"
    },
    {
      "name": "Enterprise Solution",
      "price": "Custom",
      "period": "one-time",
      "description": "Tailored for multi-location spas or large salon groups requiring bespoke features and advanced integrations.",
      "features": [
        "Fully Bespoke Development",
        "Custom Integrations",
        "Advanced Marketing Funnels",
        "Dedicated Account Manager",      ],
      "highlight": false,
      "cta": "Request Consultation"
    }
  ],
  "process": [
    {
      "step": "01",
      "title": "Discovery & Strategy",
      "description": "We delve deep into your brand, target audience, and business goals, focusing on your unique position in the Bristol market.",
      "duration": "Week 1"
    },
    {
      "step": "02",
      "title": "Design & Development",
      "description": "Our expert team crafts a visually stunning and functionally robust website, with regular feedback loops for your approval.",
      "duration": "Week 2-4"
    },
    {
      "step": "03",
      "title": "Content & Optimisation",
      "description": "Populating your site with compelling copy and optimising for search engines, ensuring Bristol clients find you easily.",
      "duration": "Week 5-6"
    },
    {
      "step": "04",
      "title": "Launch & Support",
      "description": "Your new website goes live, backed by our ongoing support and performance monitoring to ensure continued success.",
      "duration": "Week 7+"
    }
  ],
  "featuredTestimonial": {
    "quote": "WebforLeads completely transformed our online presence. Our bookings from Bristol clients have soared, and the website truly reflects the premium experience we offer. They understood our vision perfectly.",
    "name": "Sarah Jenkins",
    "role": "Owner",
    "company": "The Beauty Retreat, Clifton",
    "rating": 5,
    "result": "340%",
    "resultLabel": "Increase in Online Bookings"
  },
  "testimonials": [
    {
      "quote": "Our new website is simply stunning and so easy for clients to use. We've seen a significant uplift in enquiries from across Bristol.",
      "name": "Mark Davies",
      "role": "Manager",
      "company": "Hair & Co., Redland",
      "industry": "Hair Salon",
      "metric": "315%",
      "metricLabel": "Online Enquiries",
      "avatar": "EP",    },
    {
      "quote": "WebforLeads delivered a fantastic site that perfectly captures our spa's serene atmosphere. Their local Bristol knowledge was invaluable.",
      "name": "Eleanor Rose",
      "role": "Director",
      "company": "Bristol Wellness Spa",
      "industry": "Spa & Wellness",
      "metric": "200%",
      "metricLabel": "Website Traffic",
      "avatar": "ER",    },
    {
      "quote": "From concept to launch, the team was professional and highly effective. Our salon now truly stands out online in the competitive Bristol scene.",
      "name": "David Price",
      "role": "Founder",
      "company": "Urban Cuts, Southville",
      "industry": "Barber Shop",
      "metric": "400%",
      "metricLabel": "Client Engagement",
      "avatar": "DP",    }
  ],
  "faqCategories": [
    {
      "category": "Process & Timeline",
      "icon": <Rocket className="h-6 w-6" />,
      "questions": [
        {
          "q": "How long does a typical salon/spa website take to build?",
          "a": "Most projects are completed within 6-8 weeks, depending on complexity and client feedback. We work efficiently to get your Bristol business online quickly."
        },
        {
          "q": "What is your design process?",
          "a": "Our process involves discovery, wireframing, design mock-ups, development, content integration, SEO optimisation, and final launch, with client collaboration at every stage."
        },
        {
          "q": "Can I see progress during development?",
          "a": "Absolutely. We provide regular updates and access to a staging site so you can monitor progress and provide feedback throughout the project."
        }
      ]
    },
    {
      "category": "Services & Features",
      "icon": <Target className="h-6 w-6" />,
      "questions": [
        {
          "q": "Will my website be mobile-friendly?",
          "a": "Yes, all websites we design are fully responsive, ensuring a seamless and beautiful experience on all devices, from smartphones to desktops."
        },
        {
          "q": "Can you integrate my existing booking system?",
          "a": "Yes, we specialise in integrating popular salon and spa booking systems like Fresha, Phorest, Treatwell, and more, ensuring a smooth client journey."
        },
        {
          "q": "Do you provide content for the website?",
          "a": "We offer content creation support, including copywriting and image selection, to ensure your Bristol salon's message is clear and compelling."
        }
      ]
    },
    {
      "category": "Technical & Pricing",
      "icon": <BarChart3 className="h-6 w-6" />,
      "questions": [
        {
          "q": "What are the ongoing costs after launch?",
          "a": "Beyond the one-time build cost, there are standard annual fees for hosting, domain registration, and optional ongoing maintenance and SEO packages."
        },
        {
          "q": "Do you offer SEO services for salons in Bristol?",
          "a": "Yes, our websites are built with SEO best practices, and we offer dedicated local SEO services to help your Bristol salon rank higher in search results."
        },
        {
          "q": "What if I need updates or changes after the website is live?",
          "a": "We offer various support and maintenance packages to ensure your website remains up-to-date, secure, and performing optimally. Just get in touch!"
        }
      ]
    }
  ],
  "finalCTA": {
    "badge": "Get Started",
    "title": "Ready to Elevate Your Bristol Salon or Spa Online?",
    "description": "Partner with WebforLeads to create a stunning, high-performing website that attracts more clients and boosts your bookings across Bristol and the South West."
  },
  "structuredData": {
    "serviceName": "Salon and Spa Website Design Bristol",
    "description": "WebforLeads crafts bespoke, conversion-focused website designs for salons and spas in Bristol, driving bookings and enhancing online presence.",
    "priceRange": "£3500-Custom",
    "areaServed": "Bristol",
    "aggregateRating": {
      "ratingValue": "4.9",
      "reviewCount": "127"
    }
  }
};

export const metadata: Metadata = {
  title: serviceConfig.metaTitle,
  description: serviceConfig.metaDescription,
  keywords: serviceConfig.keywords.join(", "),
  openGraph: {
    title: serviceConfig.metaTitle,
    description: serviceConfig.metaDescription,
    type: 'website',
    url: `https://webforleads.uk/web-design/${serviceConfig.serviceSlug}`,
  },
  twitter: {
    card: 'summary_large_image',
    title: serviceConfig.metaTitle,
    description: serviceConfig.metaDescription,
  },
  alternates: {
    canonical: `https://webforleads.uk/web-design/${serviceConfig.serviceSlug}`,
  },
};

export default function SalonAndSpaWebsiteDesignBristolPage() {
  return <ServiceTemplate config={serviceConfig} />;
}


