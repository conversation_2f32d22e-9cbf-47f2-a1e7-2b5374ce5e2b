import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';
import { updateBulkJob, type BulkGenerationJob, type GeneratedPage } from '@/lib/bulk-pages-storage';
import { getTemplateById, type ServiceConfig } from '../../../site-pages/template-registry';
import { registerRoute } from '@/lib/route-registry';

// Generate config with proper React icon components
function generateConfigWithIcons(config: ServiceConfig): string {
  // Convert the config to a string and replace icon references with actual React components
  let configString = JSON.stringify(config, null, 2);

  // Replace icon string references with React components
  const iconReplacements = {
    '"TrendingUp"': '<TrendingUp className="h-6 w-6" />',
    '"Target"': '<Target className="h-6 w-6" />',
    '"Users"': '<Users className="h-6 w-6" />',
    '"Rocket"': '<Rocket className="h-6 w-6" />',
    '"BarChart3"': '<BarChart3 className="h-6 w-6" />',
    '"Search"': '<Search className="h-6 w-6" />',
    '"Globe"': '<Globe className="h-6 w-6" />',
    '"Zap"': '<Zap className="h-6 w-6" />',
    '"Brain"': '<Brain className="h-8 w-8" />',
    '"Gauge"': '<Gauge className="h-8 w-8" />',
    '"Smartphone"': '<Smartphone className="h-6 w-6" />',
    '"Palette"': '<Palette className="h-6 w-6" />',
    '"Code"': '<Code className="h-6 w-6" />',
    '"DollarSign"': '<DollarSign className="h-6 w-6" />',
    '"MousePointer"': '<MousePointer className="h-6 w-6" />',
    '"Share2"': '<Share2 className="h-6 w-6" />',
    '"Heart"': '<Heart className="h-6 w-6" />',
    '"MessageCircle"': '<MessageCircle className="h-6 w-6" />',
    '"Camera"': '<Camera className="h-6 w-6" />',
    '"Tablet"': '<Tablet className="h-6 w-6" />',
    '"Monitor"': '<Monitor className="h-6 w-6" />'
  };

  // Apply replacements
  Object.entries(iconReplacements).forEach(([search, replace]) => {
    configString = configString.replace(new RegExp(search, 'g'), replace);
  });

  return configString;
}

// --- Helpers: robust JSON extraction/repair ---
function stripCodeFences(text: string): string {
  return text
    .replace(/```json/gi, '```')
    .replace(/```/g, '')
    .trim();
}

function normalizeSmartQuotes(text: string): string {
  return text
    .replace(/[\u2018\u2019]/g, "'")
    .replace(/[\u201C\u201D]/g, '"');
}

function extractFirstJsonObject(text: string): string | null {
  const s = normalizeSmartQuotes(stripCodeFences(text));
  const start = s.indexOf('{');
  if (start === -1) return null;
  let depth = 0;
  let inString = false;
  let escape = false;
  for (let i = start; i < s.length; i++) {
    const ch = s[i];
    if (inString) {
      if (escape) {
        escape = false;
      } else if (ch === '\\') {
        escape = true;
      } else if (ch === '"') {
        inString = false;
      }
      continue;
    } else {
      if (ch === '"') {
        inString = true;
      } else if (ch === '{') {
        depth++;
      } else if (ch === '}') {
        depth--;
        if (depth === 0) {
          return s.substring(start, i + 1);
        }
      }
    }
  }
  return null;
}

function sanitizeJson(text: string): string {
  let s = normalizeSmartQuotes(stripCodeFences(text)).trim();
  // Remove JS-style comments just in case
  s = s.replace(/\/\/.*$/gm, '');
  s = s.replace(/\/\*[\s\S]*?\*\//g, '');
  // Remove trailing commas before } or ]
  s = s.replace(/,\s*(\}|\])/g, '$1');
  // Remove BOM
  s = s.replace(/^\uFEFF/, '');
  return s;
}

// Try extracting JSON between explicit markers if the model follows them
function extractJsonBetweenMarkers(text: string): string | null {
  const s = normalizeSmartQuotes(stripCodeFences(text));
  const begin = 'BEGIN_JSON';
  const end = 'END_JSON';
  const start = s.indexOf(begin);
  if (start === -1) return null;
  const stop = s.indexOf(end, start + begin.length);
  if (stop === -1) return null;
  const between = s.substring(start + begin.length, stop).trim();
  return between || null;
}

// Simple delay helper for retries
async function sleep(ms: number) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// Validate and fix service config to ensure proper format
function validateAndFixServiceConfig(config: any): any {
  // Fix testimonials - ensure all have avatar property
  if (config.testimonials && Array.isArray(config.testimonials)) {
    config.testimonials = config.testimonials.map((testimonial: any) => {
      if (!testimonial.avatar && testimonial.name) {
        // Generate avatar from name initials
        const nameParts = testimonial.name.split(' ');
        const initials = nameParts.map((part: string) => part.charAt(0).toUpperCase()).join('').slice(0, 3);
        testimonial.avatar = initials;
      }
      return testimonial;
    });
  }

  // Fix FAQ categories - ensure all questions are objects with q and a properties
  if (config.faqCategories && Array.isArray(config.faqCategories)) {
    config.faqCategories = config.faqCategories.map((category: any) => {
      if (category.questions && Array.isArray(category.questions)) {
        category.questions = category.questions.map((question: any) => {
          // If question is a string, convert to object
          if (typeof question === 'string') {
            return {
              q: question,
              a: "Answer will be provided during consultation."
            };
          }
          // Ensure question object has both q and a properties
          if (typeof question === 'object' && question !== null) {
            if (!question.q && !question.a) {
              return {
                q: "Question will be provided during consultation.",
                a: "Answer will be provided during consultation."
              };
            }
            if (!question.q) question.q = "Question will be provided during consultation.";
            if (!question.a) question.a = "Answer will be provided during consultation.";
          }
          return question;
        });
      }
      return category;
    });
  }

  return config;
}

// Debug helpers
const DEBUG_BULK_GEN = (process.env.DEBUG_BULK_GEN === '1' || process.env.DEBUG_BULK_GEN === 'true' || process.env.NODE_ENV !== 'production');

function slugifyForFile(name: string): string {
  return name
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, '-')
    .replace(/^-+|-+$/g, '')
    .slice(0, 80);
}

function ensureLogsDir(): string {
  const logsDir = path.join(process.cwd(), 'bulk-pages-storage', 'logs');
  if (!fs.existsSync(logsDir)) fs.mkdirSync(logsDir, { recursive: true });
  return logsDir;
}

function writeDebugFile(
  baseName: string,
  suffix: string,
  content: string | object
) {
  try {
    const dir = ensureLogsDir();
    const file = path.join(dir, `${baseName}-${suffix}`);
    const data = typeof content === 'string' ? content : JSON.stringify(content, null, 2);
    fs.writeFileSync(file, data);
  } catch (err) {
    console.warn('⚠️ Failed writing debug file', suffix, err);
  }
}

// Environment variables
const GEMINI_API_KEY = process.env.GEMINI_API_KEY;
const GEMINI_API_KEYS: string[] = (process.env.GEMINI_API_KEYS || '')
  .split(',')
  .map(s => s.trim())
  .filter(Boolean);
const GEMINI_TEXT_API_URL = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent';

// Key rotation helpers
function getAvailableGeminiKeys(): string[] {
  if (GEMINI_API_KEYS.length > 0) return GEMINI_API_KEYS;
  if (GEMINI_API_KEY) return [GEMINI_API_KEY];
  return [];
}

function selectGeminiApiKeyForPageIndex(pageIndex: number): string {
  const keys = getAvailableGeminiKeys();
  if (!keys.length) {
    throw new Error('No Gemini API keys configured. Set GEMINI_API_KEY or GEMINI_API_KEYS in environment.');
  }
  const bucket = Math.floor(pageIndex / 10); // rotate every 10 pages
  const keyIndex = bucket % keys.length;
  return keys[keyIndex];
}

function maskKey(key: string): string {
  if (!key) return '';
  return key.length > 10 ? `${key.slice(0, 4)}...${key.slice(-4)}` : '****';
}

// Interfaces moved to @/lib/bulk-pages-storage.ts and @/app/site-pages/template-registry.ts

// Interfaces moved to @/lib/bulk-pages-storage.ts and @/app/site-pages/template-registry.ts

// Generate service configuration using Gemini AI
async function generateServiceConfig(
  keyword: string,
  location: string,
  templateId: string,
  apiKey: string,
  debug?: { jobId: string; pageTitle: string; pageIndex: number }
): Promise<ServiceConfig> {
  const template = getTemplateById(templateId);
  if (!template) {
    throw new Error(`Template not found: ${templateId}`);
  }

  const prompt = `You are generating structured content for a service landing page. You are Not allwed to provide any useless data that can make json parsing fail from your response to external api bridge

CONTEXT
- Company: WebforLeads (UK digital marketing agency)
- Service Keyword: "${keyword}"
- Primary Location: "${location}" (and surrounding areas)
- Website: webforleads.uk | Email: <EMAIL> | Phone: 0800 123 4567
- Established: 2019 | Team: 15+ specialists | Awards: Google Partner, Facebook Marketing Partner
- Template: ${template.name} (Category: ${template.category}) | Typical pricing: ${template.pricing.starter} to ${template.pricing.enterprise}

OUTPUT RULES (CRITICAL)
- Return ONLY a single JSON object. No markdown, no code fences, no commentary.
- Use standard ASCII quotes (") and valid JSON formatting (no trailing commas, no comments).
- All string fields must be non-empty and written in British English.
- Strong UK localisation: naturally reference ${location} areas, nearby landmarks, boroughs, or notable business districts.
- Use WebforLeads brand tone: confident, data-driven, premium, conversion-focused.
- Icons MUST be one of these exact strings (case-sensitive): [TrendingUp, Target, Users, Rocket, BarChart3, Search, Globe, Zap, Brain, Gauge, Smartphone, Palette, Code, DollarSign, MousePointer, Share2, Heart, MessageCircle, Camera, Tablet, Monitor].
- Meta guidelines: metaTitle < 60 chars; metaDescription < 160 chars.
- Keywords: include the core phrase and local variants (5–10 items, lowercase, no punctuation except hyphens where natural).
- Metrics format: use strings like "500%", "£1.8M+", "96%", "24hrs".
- Testimonials: realistic UK business names from or near ${location}; rating is an integer 1–5. EVERY testimonial MUST have ALL properties including "avatar" with 2-3 initials.
- FAQ questions: EVERY question MUST be an object with "q" and "a" properties. NO string questions allowed.
- Packages: price as "£X" or "Custom"; period may be "one-time" or an empty string.
- Slug formatting: lowercase kebab-case; no special characters beyond hyphens.
 - Wrap the JSON output strictly between two lines containing only these markers:
BEGIN_JSON
[the JSON object]
END_JSON
 - Do not output anything before BEGIN_JSON or after END_JSON.

CONTENT REQUIREMENTS
- Explicitly tailor copy to ${location} (mention areas/neighbourhoods, notable roads, business hubs, relevant local industries).
- Avoid placeholders like [agency name] or [city]. Use WebforLeads and ${location} explicitly.
- Keep copy concise, persuasive, and specific. Avoid generic claims without context.

CRITICAL FORMAT REQUIREMENTS:
- TESTIMONIALS: Each testimonial object MUST have ALL 8 properties: quote, name, role, company, industry, metric, metricLabel, avatar. NO missing properties.
- FAQ QUESTIONS: Each question MUST be an object with "q" and "a" properties. NEVER use plain strings like "Question text".
- COMMAS: Ensure proper comma placement after each property, especially after "metricLabel" when followed by "avatar".

  Return ONLY a valid JSON object matching EXACTLY this structure (property names and nesting must match). Place it between BEGIN_JSON and END_JSON markers as shown:
  BEGIN_JSON
  {
  "serviceName": "Service name (e.g., Web Design)",
  "serviceSlug": "service-slug",
  "metaTitle": "SEO-optimized title under 60 characters",
  "metaDescription": "SEO-optimized description under 160 characters",
  "keywords": ["primary keyword", "secondary keyword", "tertiary keyword"],
  "heroTitle": "Compelling hero title",
  "heroSubtitle": "Supporting subtitle",
  "heroDescription": "Engaging description paragraph",
  "heroBadgeText": "BADGE TEXT",
  "stats": [
    {"number": "500%", "label": "Stat label", "icon": "TrendingUp"},
    {"number": "£1.8M+", "label": "Stat label", "icon": "Target"},
    {"number": "96%", "label": "Stat label", "icon": "Users"},
    {"number": "24hrs", "label": "Stat label", "icon": "Rocket"}
  ],
  "designFeatures": [
    {"icon": "Brain", "title": "Feature title", "description": "Feature description", "highlight": "Highlight text"},
    {"icon": "Gauge", "title": "Feature title", "description": "Feature description", "highlight": "Highlight text"},
    {"icon": "Target", "title": "Feature title", "description": "Feature description", "highlight": "Highlight text"},
    {"icon": "BarChart3", "title": "Feature title", "description": "Feature description", "highlight": "Highlight text"}
  ],
  "features": [
    {"icon": "Search", "title": "Feature title", "description": "Feature description"},
    {"icon": "Target", "title": "Feature title", "description": "Feature description"},
    {"icon": "Globe", "title": "Feature title", "description": "Feature description"},
    {"icon": "Zap", "title": "Feature title", "description": "Feature description"},
    {"icon": "BarChart3", "title": "Feature title", "description": "Feature description"},
    {"icon": "Users", "title": "Feature title", "description": "Feature description"}
  ],
  "packages": [
    {"name": "Package name", "price": "£3,500", "period": "one-time", "description": "Package description", "features": ["Feature 1", "Feature 2"], "highlight": false, "cta": "CTA text"},
    {"name": "Package name", "price": "£7,500", "period": "one-time", "description": "Package description", "features": ["Feature 1", "Feature 2"], "highlight": true, "cta": "CTA text"},
    {"name": "Package name", "price": "Custom", "period": "one-time", "description": "Package description", "features": ["Feature 1", "Feature 2"], "highlight": false, "cta": "CTA text"}
  ],
  "process": [
    {"step": "01", "title": "Step title", "description": "Step description", "duration": "Week 1"},
    {"step": "02", "title": "Step title", "description": "Step description", "duration": "Week 2-3"},
    {"step": "03", "title": "Step title", "description": "Step description", "duration": "Week 4-5"},
    {"step": "04", "title": "Step title", "description": "Step description", "duration": "Week 6+"}
  ],
  "featuredTestimonial": {
    "quote": "Testimonial quote",
    "name": "Client name",
    "role": "Client role",
    "company": "Company name",
    "rating": 5,
    "result": "340%",
    "resultLabel": "Result label"
  },
  "testimonials": [
    {"quote": "Quote", "name": "Name", "role": "Role", "company": "Company", "industry": "Industry", "metric": "315%", "metricLabel": "Metric label", "avatar": "MC"},
    {"quote": "Quote", "name": "Name", "role": "Role", "company": "Company", "industry": "Industry", "metric": "200%", "metricLabel": "Metric label", "avatar": "ER"},
    {"quote": "Quote", "name": "Name", "role": "Role", "company": "Company", "industry": "Industry", "metric": "400%", "metricLabel": "Metric label", "avatar": "DP"}
  ],
  "faqCategories": [
    {
      "category": "Process & Timeline",
      "icon": "Rocket",
      "questions": [
        {"q": "Question?", "a": "Answer"},
        {"q": "Question?", "a": "Answer"},
        {"q": "Question?", "a": "Answer"}
      ]
    },
    {
      "category": "Services & Features",
      "icon": "Target",
      "questions": [
        {"q": "Question?", "a": "Answer"},
        {"q": "Question?", "a": "Answer"},
        {"q": "Question?", "a": "Answer"}
      ]
    },
    {
      "category": "Technical & Pricing",
      "icon": "BarChart3",
      "questions": [
        {"q": "Question?", "a": "Answer"},
        {"q": "Question?", "a": "Answer"},
        {"q": "Question?", "a": "Answer"}
      ]
    }
  ],
  "finalCTA": {
    "badge": "Get Started",
    "title": "CTA title",
    "description": "CTA description"
  },
  "structuredData": {
    "serviceName": "Service name",
    "description": "Service description",
    "priceRange": "£3000-£15000",
    "areaServed": "${location}",
    "aggregateRating": {
      "ratingValue": "4.9",
      "reviewCount": "127"
    }
  }
}
END_JSON`;

  try {
    console.log('🤖 Generating service config with Gemini AI');
    console.log('🔑 Keyword:', keyword);
    console.log('📍 Location:', location);
    if (DEBUG_BULK_GEN) {
      console.log('🧪 Template:', template.name, '| Category:', template.category);
      console.log('📝 Prompt length:', prompt.length);
      console.log('📝 Prompt preview:', prompt.slice(0, 300));
    }

    let lastError: unknown = null;

    for (let attempt = 1; attempt <= 3; attempt++) {
      try {
        const baseName = debug
          ? `${debug.jobId}-${String(debug.pageIndex).padStart(3, '0')}-${slugifyForFile(debug.pageTitle)}-attempt${attempt}`
          : `nojob-attempt${attempt}`;

        if (DEBUG_BULK_GEN) {
          writeDebugFile(baseName, 'prompt.txt', prompt);
        }

        const response = await fetch(`${GEMINI_TEXT_API_URL}?key=${apiKey}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            contents: [{
              parts: [{ text: prompt }]
            }],
            generationConfig: {
              temperature: 0.2,
              maxOutputTokens: 12000,
              response_mime_type: "application/json"
            }
          }),
        });

        if (!response.ok) {
          throw new Error(`Gemini API error: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();

        if (DEBUG_BULK_GEN) {
          console.log(`📥 Attempt ${attempt} response status:`, response.status);
          console.log('📥 Candidates count:', Array.isArray(data?.candidates) ? data.candidates.length : 0);
          writeDebugFile(baseName, 'raw-response.json', data);
        }

        // Aggregate all text parts from all candidates to avoid empty/partial content
        const candidates = Array.isArray(data?.candidates) ? data.candidates : [];
        const texts: string[] = [];
        for (const c of candidates) {
          const parts = c?.content?.parts ?? [];
          for (const p of parts) {
            if (p && typeof p.text === 'string') texts.push(p.text);
          }
        }
        const generatedText = texts.join('\n').trim();

        if (!generatedText) {
          if (DEBUG_BULK_GEN) {
            writeDebugFile(baseName, 'no-generated-text.txt', {
              message: 'Empty aggregated text from candidates',
              candidatesCount: candidates.length,
              partsPerCandidate: candidates.map((c: any) => c?.content?.parts?.length ?? 0),
            });
          }
          throw new Error('No content generated by Gemini');
        }

        if (DEBUG_BULK_GEN) {
          console.log(`🧩 Generated text length: ${generatedText.length}`);
          console.log('🧩 Generated text preview:', generatedText.slice(0, 400));
          writeDebugFile(baseName, 'generated-text.txt', generatedText);
        }

        // Parse JSON from generated text with multiple fallbacks
        let candidateJson: string | null = null;
        // 1) Marker-based extraction
        const markerExtract = extractJsonBetweenMarkers(generatedText);
        if (markerExtract) {
          candidateJson = markerExtract;
          if (DEBUG_BULK_GEN) {
            writeDebugFile(baseName, 'extracted-from-markers.txt', markerExtract.slice(0, 1000));
          }
        }
        // 2) Balanced-object extractor
        if (!candidateJson) {
          const balanced = extractFirstJsonObject(generatedText);
          if (balanced) {
            candidateJson = balanced;
            if (DEBUG_BULK_GEN) {
              writeDebugFile(baseName, 'extracted-balanced.txt', balanced.slice(0, 1000));
            }
          }
        }
        // 3) First/last brace pragmatic fallback
        if (!candidateJson) {
          const firstBrace = generatedText.indexOf('{');
          const lastBrace = generatedText.lastIndexOf('}');
          if (firstBrace >= 0 && lastBrace > firstBrace) {
            const span = generatedText.slice(firstBrace, lastBrace + 1);
            candidateJson = span;
            if (DEBUG_BULK_GEN) {
              writeDebugFile(baseName, 'extracted-by-brace-range.txt', {
                firstBrace,
                lastBrace,
                preview: span.slice(0, 1000)
              });
            }
          }
        }
        if (!candidateJson) {
          if (DEBUG_BULK_GEN) {
            const hasFence = /```/.test(generatedText);
            const firstBrace = generatedText.indexOf('{');
            const lastBrace = generatedText.lastIndexOf('}');
            writeDebugFile(baseName, 'extraction-failed.txt', {
              reason: 'No JSON found after all extraction strategies',
              triedMarkers: true,
              triedBalanced: true,
              triedBraceRange: true,
              hasFence,
              firstBrace,
              lastBrace,
              preview: generatedText.slice(0, 1000),
            });
          }
          throw new Error('No valid JSON object found in generated content');
        }

        let config: ServiceConfig;
        try {
          config = JSON.parse(candidateJson) as ServiceConfig;
        } catch (e) {
          const repaired = sanitizeJson(candidateJson);
          try {
            config = JSON.parse(repaired) as ServiceConfig;
          } catch (e2) {
            if (DEBUG_BULK_GEN) {
              writeDebugFile(baseName, 'parse-error.txt', {
                error: e instanceof Error ? e.message : String(e),
                extractedPreview: candidateJson.slice(0, 1000),
                repairedPreview: repaired.slice(0, 1000),
              });
            }
            throw e2;
          }
        }
        console.log('✅ Service config generated successfully (attempt', attempt, ')');
        return config;
      } catch (err) {
        lastError = err;
        console.warn(`⚠️ Attempt ${attempt} failed:`, err instanceof Error ? err.message : String(err));
        if (attempt < 3) {
          await sleep(1000 * attempt);
          continue;
        }
      }
    }

    throw lastError ?? new Error('Failed to generate service config');
  } catch (error) {
    console.error('❌ Error generating service config:', error);
    throw error;
  }
}

// Create page file from service config - simple individual folder approach
async function createPageFile(config: ServiceConfig, slug: string, templateId: string): Promise<string> {
  const template = getTemplateById(templateId);
  if (!template) {
    throw new Error(`Template not found: ${templateId}`);
  }

  // Map template IDs to folder names
  const templateFolderMap: { [key: string]: string } = {
    'web-design': 'web-design',
    'seo': 'seo-services',
    'google-ads': 'google-ads',
    'social-media': 'social-media',
    'app-development': 'app-development',
    'digital-marketing': 'digital-marketing'
  };

  const serviceFolder = templateFolderMap[templateId];
  if (!serviceFolder) {
    throw new Error(`No folder mapping found for template: ${templateId}`);
  }

  // Generate component name from slug
  const componentName = slug
    .split('-')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join('') + 'Page';

  const pageContent = `import { Metadata } from 'next';
import ServiceTemplate from '../../site-pages/service-template';
import {
  Palette, Smartphone, TrendingUp, Search, Brain, Gauge,
  Target, Users, Rocket, BarChart3, Globe, Zap, Code,
  DollarSign, MousePointer, Share2, Heart, MessageCircle,
  Camera, Tablet, Monitor
} from "lucide-react";

// ${config.serviceName} Service Configuration for ${slug}
// Generated using ${template.name} template for WebforLeads
const serviceConfig = ${generateConfigWithIcons(config)};

export const metadata: Metadata = {
  title: serviceConfig.metaTitle,
  description: serviceConfig.metaDescription,
  keywords: serviceConfig.keywords.join(", "),
  openGraph: {
    title: serviceConfig.metaTitle,
    description: serviceConfig.metaDescription,
    type: 'website',
    url: \`https://webforleads.uk/${serviceFolder}/\${serviceConfig.serviceSlug}\`,
  },
  twitter: {
    card: 'summary_large_image',
    title: serviceConfig.metaTitle,
    description: serviceConfig.metaDescription,
  },
  alternates: {
    canonical: \`https://webforleads.uk/${serviceFolder}/\${serviceConfig.serviceSlug}\`,
  },
};

export default function ${componentName}() {
  return <ServiceTemplate config={serviceConfig} />;
}`;

  // Create individual folder for each page
  const pageDir = path.join(process.cwd(), 'src', 'app', serviceFolder, slug);
  const pageFile = path.join(pageDir, 'page.tsx');

  if (!fs.existsSync(pageDir)) {
    fs.mkdirSync(pageDir, { recursive: true });
  }

  fs.writeFileSync(pageFile, pageContent);

  console.log('📄 Page file created:', pageFile);
  console.log('📁 Service folder:', serviceFolder);
  console.log('🔗 URL will be:', `/${serviceFolder}/${slug}`);
  return pageFile;
}



export async function POST(request: NextRequest) {
  try {
    console.log('\n🚀 STARTING BULK GENERATION PROCESS');
    
    const { jobId } = await request.json();
    
    if (!jobId) {
      return NextResponse.json(
        { success: false, error: 'Job ID is required' },
        { status: 400 }
      );
    }

    console.log('🆔 Job ID:', jobId);

    // Update job status to running
    const job = updateBulkJob(jobId, { 
      status: 'running',
      progress: 0
    });

    if (!job) {
      return NextResponse.json(
        { success: false, error: 'Job not found' },
        { status: 404 }
      );
    }

    console.log('✅ Job status updated to running');

    // Start background generation process
    generatePagesInBackground(jobId);

    return NextResponse.json({
      success: true,
      message: 'Generation started successfully',
      jobId
    });

  } catch (error) {
    console.error('💥 START GENERATION ERROR:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to start generation',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// Background generation process
async function generatePagesInBackground(jobId: string) {
  console.log('🔄 Starting background generation for job:', jobId);
  
  // This will run in the background
  // In a production environment, you'd want to use a proper job queue
  setTimeout(async () => {
    await processJobPages(jobId);
  }, 1000);
}

// Process all pages in a job
async function processJobPages(jobId: string) {
  try {
    const job = updateBulkJob(jobId, {});
    if (!job) return;

    console.log('📊 Processing', job.pages.length, 'pages for job:', jobId);

    for (let i = 0; i < job.pages.length; i++) {
      const page = job.pages[i];
      
      try {
        console.log(`🔄 Processing page ${i + 1}/${job.pages.length}: ${page.title}`);
        
        // Update page status to generating
        job.pages[i].status = 'generating';
        updateBulkJob(jobId, { pages: job.pages });

        // Generate service config with AI using template
        const selectedKey = selectGeminiApiKeyForPageIndex(i);
        if (DEBUG_BULK_GEN) {
          console.log('🔐 Gemini key selected (masked):', maskKey(selectedKey), 'for page index', i);
        }
        const rawConfig = await generateServiceConfig(
          page.keyword,
          page.location,
          job.templateId,
          selectedKey,
          { jobId, pageTitle: page.title, pageIndex: i + 1 }
        );

        // Validate and fix the generated config to ensure proper format
        const config = validateAndFixServiceConfig(rawConfig);
        console.log('✅ Config validated and fixed for proper testimonials and FAQ format');

        // Update config with proper slug and service name
        config.serviceSlug = page.slug;

        // Create page file
        const filePath = await createPageFile(config, page.slug, job.templateId);
        
        // Register route in route registry
        registerRoute({
          slug: page.slug,
          title: page.title,
          templateId: job.templateId,
          templateName: job.templateName,
          keyword: page.keyword,
          location: page.location,
          filePath,
          createdAt: new Date().toISOString(),
          jobId: job.id,
          status: 'active'
        });

        // Update page status to completed
        job.pages[i].status = 'completed';
        job.pages[i].filePath = filePath;
        job.pages[i].progress = 100;

        // Update job progress
        const completedPages = job.pages.filter((p: { status: string }) => p.status === 'completed').length;
        const progress = Math.round((completedPages / job.pages.length) * 100);
        
        updateBulkJob(jobId, {
          pages: job.pages,
          completedPages,
          progress,
          status: progress === 100 ? 'completed' : 'running'
        });

        console.log(`✅ Page completed: ${page.title} (${completedPages}/${job.pages.length})`);

        // Add delay to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 2000));

      } catch (error) {
        console.error(`❌ Error processing page ${page.title}:`, error);
        
        // Update page status to error
        job.pages[i].status = 'error';
        job.pages[i].error = error instanceof Error ? error.message : 'Unknown error';
        
        updateBulkJob(jobId, { pages: job.pages });
      }
    }

    console.log('🎉 Bulk generation completed for job:', jobId);

    // Revalidate sitemap after bulk generation completes
    try {
      console.log('🗺️ Revalidating sitemap after bulk page generation...');
      await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3001'}/api/revalidate-sitemap`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ type: 'bulk-pages', jobId })
      });
      console.log('✅ Sitemap revalidation triggered');
    } catch (sitemapError) {
      console.warn('⚠️ Failed to revalidate sitemap:', sitemapError);
      // Don't fail the generation if sitemap revalidation fails
    }

  } catch (error) {
    console.error('💥 Error in background generation:', error);
    updateBulkJob(jobId, { status: 'error' });
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Bulk generation start API endpoint. Use POST to start generation for a job.',
    endpoints: {
      'POST /api/bulk-pages/start-generation': 'Start generation process for a bulk job'
    }
  });
}
