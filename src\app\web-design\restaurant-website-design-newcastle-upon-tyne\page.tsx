import { Metadata } from 'next';
import ServiceTemplate from '../../site-pages/service-template';
import {
  Palette, Smartphone, TrendingUp, Search, Brain, Gauge,
  Target, Users, Rocket, BarChart3, Globe, Zap, Code,
  DollarSign, MousePointer, Share2, Heart, MessageCircle,
  Camera, Tablet, Monitor
} from "lucide-react";

// Restaurant Website Design Service Configuration for restaurant-website-design-newcastle-upon-tyne
// Generated using Web Design Services template for WebforLeads
const serviceConfig = {
  "serviceName": "Restaurant Website Design",
  "serviceSlug": "restaurant-website-design-newcastle-upon-tyne",
  "metaTitle": "Restaurant Website Design Newcastle | WebforLeads",
  "metaDescription": "Elevate your Newcastle restaurant with a stunning, conversion-focused website. WebforLeads crafts bespoke designs for eateries across Tyne & Wear.",
  "keywords": [
    "restaurant website design newcastle",
    "newcastle restaurant web design",
    "website for restaurants uk",
    "online ordering website newcastle",
    "bespoke restaurant websites",
    "web design for cafes newcastle",
    "pub website design north east",  ],
  "heroTitle": "Bespoke Restaurant Website Design for Newcastle's Culinary Scene",
  "heroSubtitle": "Captivate Diners, Streamline Bookings, and Boost Your Revenue Across Tyne & Wear",
  "heroDescription": "WebforLeads specialises in crafting high-performance, visually stunning websites for restaurants, cafes, and pubs across Newcastle upon Tyne and the wider North East. From the bustling Quayside to the vibrant streets of Jesmond, we design digital experiences that reflect your unique brand, drive online reservations, and simplify menu management, ensuring your establishment stands out.",
  "heroBadgeText": "CONVERSION-FOCUSED DESIGN",
  "stats": [
    {
      "number": "340%",
      "label": "Average ROI for Clients",
      "icon": <TrendingUp className="h-6 w-6" />
    },
    {
      "number": "£1.8M+",
      "label": "Generated for Clients",
      "icon": <DollarSign className="h-6 w-6" />
    },
    {
      "number": "96%",
      "label": "Client Satisfaction Rate",
      "icon": <Heart className="h-6 w-6" />
    },
    {
      "number": "24hrs",
      "label": "Initial Concept Delivery",
      "icon": <Rocket className="h-6 w-6" />
    }
  ],
  "designFeatures": [
    {
      "icon": <Smartphone className="h-6 w-6" />,
      "title": "Mobile-First Responsiveness",
      "description": "Your website will look flawless and function perfectly on any device, from smartphones to desktops, ensuring a seamless experience for diners on the go.",
      "highlight": "Optimised for all devices"
    },
    {
      "icon": <Palette className="h-6 w-6" />,
      "title": "Stunning Visual Menus",
      "description": "Showcase your culinary creations with high-resolution imagery and intuitive menu layouts that entice customers and drive ordering decisions.",
      "highlight": "Appetite-inducing visuals"
    },
    {
      "icon": <MousePointer className="h-6 w-6" />,
      "title": "Integrated Booking & Ordering",
      "description": "Seamlessly integrate online reservation systems or direct ordering platforms, making it effortless for customers to book a table or place a takeaway order.",
      "highlight": "Direct revenue channels"
    },
    {
      "icon": <Search className="h-6 w-6" />,
      "title": "Local SEO Optimisation",
      "description": "Ensure your restaurant ranks prominently in local search results for 'restaurants in Newcastle' or 'cafes near Jesmond', attracting more footfall and online traffic.",
      "highlight": "Dominate local search"
    }
  ],
  "features": [
    {
      "icon": <Code className="h-6 w-6" />,
      "title": "Custom Web Development",
      "description": "Tailored solutions built from the ground up to meet your restaurant's specific needs and brand identity, ensuring a unique online presence."
    },
    {
      "icon": <Target className="h-6 w-6" />,
      "title": "Conversion Rate Optimisation",
      "description": "Strategically designed calls-to-action and user flows to maximise online bookings, takeaway orders, and customer enquiries."
    },
    {
      "icon": <Globe className="h-6 w-6" />,
      "title": "Secure & Scalable Hosting",
      "description": "Reliable, fast, and secure hosting solutions that ensure your website is always accessible and performs optimally, even during peak times."
    },
    {
      "icon": <Zap className="h-6 w-6" />,
      "title": "Lightning-Fast Load Times",
      "description": "Optimised for speed, our websites provide an instant user experience, reducing bounce rates and improving search engine rankings."
    },
    {
      "icon": <BarChart3 className="h-6 w-6" />,
      "title": "Analytics & Reporting",
      "description": "Gain valuable insights into your website's performance with comprehensive analytics, helping you understand customer behaviour and refine your strategy."
    },
    {
      "icon": <Users className="h-6 w-6" />,
      "title": "Ongoing Support & Maintenance",
      "description": "Our dedicated team provides continuous support, updates, and maintenance to keep your restaurant's website running smoothly and securely."
    }
  ],
  "packages": [
    {
      "name": "Starter Menu Website",
      "price": "£3,500",
      "period": "one-time",
      "description": "Ideal for new eateries or those needing a professional online presence with core features.",
      "features": [
        "Bespoke 5-page design",
        "Mobile-responsive layout",
        "Basic menu display",
        "Contact form & map",      ],
      "highlight": false,
      "cta": "View Details"
    },
    {
      "name": "Signature Dining Website",
      "price": "£7,500",
      "period": "one-time",
      "description": "Our most popular package, designed for established restaurants seeking advanced functionality and growth.",
      "features": [
        "Up to 10 custom pages",
        "Integrated online booking system",
        "Advanced menu management",
        "High-res photo gallery",
        "Local SEO optimisation",      ],
      "highlight": true,
      "cta": "Get a Quote"
    },
    {
      "name": "Gourmet Bespoke Solution",
      "price": "Custom",
      "period": "one-time",
      "description": "For multi-location restaurants or unique concepts requiring highly customised features and integrations.",
      "features": [
        "Unlimited custom pages",
        "Advanced online ordering & delivery integration",
        "CRM integration",
        "Multi-language support",
        "Dedicated account manager",      ],
      "highlight": false,
      "cta": "Discuss Your Project"
    }
  ],
  "process": [
    {
      "step": "01",
      "title": "Discovery & Strategy",
      "description": "We begin by understanding your restaurant's unique brand, target audience, and business goals. This includes a deep dive into your menu, service style, and competitive landscape in Newcastle.",
      "duration": "Week 1"
    },
    {
      "step": "02",
      "title": "Design & Development",
      "description": "Our designers craft a visually stunning, user-friendly website mock-up. Upon approval, our developers bring it to life, integrating all necessary features like online booking or menu systems.",
      "duration": "Week 2-3"
    },
    {
      "step": "03",
      "title": "Content & Optimisation",
      "description": "We assist with populating your site with engaging content, high-quality food photography, and optimise it for local search engines, ensuring your restaurant is easily found by diners in Newcastle.",
      "duration": "Week 4-5"
    },
    {
      "step": "04",
      "title": "Launch & Support",
      "description": "After rigorous testing, your new website goes live! We provide ongoing support, maintenance, and performance monitoring to ensure your digital presence continues to drive success.",
      "duration": "Week 6+"
    }
  ],
  "featuredTestimonial": {
    "quote": "WebforLeads transformed our online presence. Our bookings from the website have soared, and the new menu system is incredibly easy to manage. They truly understand the Newcastle hospitality market.",
    "name": "Sarah Jenkins",
    "role": "Owner",
    "company": "The Quayside Bistro",
    "rating": 5,
    "result": "340%",
    "resultLabel": "Increase in Online Bookings"
  },
  "testimonials": [
    {
      "quote": "Our new website from WebforLeads perfectly captures the essence of our Gosforth cafe. It's sleek, easy to navigate, and has significantly boosted our takeaway orders.",
      "name": "Mark Collins",
      "role": "Manager",
      "company": "Gosforth Grind",
      "industry": "Cafe",
      "metric": "315%",
      "metricLabel": "Increase in Takeaway Orders",
      "avatar": "SMW",    },
    {
      "quote": "As a fine dining establishment in the city centre, our website needs to reflect our premium brand. WebforLeads delivered a sophisticated, high-performing site that truly impresses.",
      "name": "Eleanor Reid",
      "role": "Head Chef",
      "company": "Grey Street Supper Club",
      "industry": "Fine Dining",
      "metric": "200%",
      "metricLabel": "Improvement in Brand Perception",
      "avatar": "ER",    },
    {
      "quote": "From concept to launch, WebforLeads made the process seamless. Our pub's new site is fantastic for showcasing our events and has made table reservations a breeze for our regulars.",
      "name": "David Price",
      "role": "Proprietor",
      "company": "The Ouseburn Arms",
      "industry": "Pub",
      "metric": "400%",
      "metricLabel": "Increase in Event Enquiries",
      "avatar": "DP",    }
  ],
  "faqCategories": [
    {
      "category": "Process & Timeline",
      "icon": <Rocket className="h-6 w-6" />,
      "questions": [
        {
          "q": "How long does it take to build a restaurant website?",
          "a": "Typically, a bespoke restaurant website takes 6-8 weeks from initial consultation to launch, depending on the complexity and features required. We work efficiently to get you online quickly."
        },
        {
          "q": "Can I update my menu and content myself?",
          "a": "Absolutely. All our websites are built with user-friendly Content Management Systems (CMS) like WordPress, allowing you to easily update menus, photos, and promotions without technical expertise."
        },
        {
          "q": "What is the typical process for designing my restaurant's website?",
          "a": "Our process involves discovery, design mock-ups, development, content integration, optimisation, and finally, launch. We keep you informed and involved at every stage to ensure your vision is realised."
        }
      ]
    },
    {
      "category": "Services & Features",
      "icon": <Target className="h-6 w-6" />,
      "questions": [
        {
          "q": "Do you integrate online booking and ordering systems?",
          "a": "Yes, we specialise in integrating popular online booking platforms (e.g., OpenTable, Resy) and custom online ordering systems directly into your website for a seamless customer experience."
        },
        {
          "q": "Will my website be mobile-friendly?",
          "a": "Every website we design is built with a mobile-first approach, ensuring it looks and functions perfectly on all devices, from smartphones to tablets and desktops."
        },
        {
          "q": "Can you help with food photography for my website?",
          "a": "While not included in all packages, we can connect you with professional food photographers in Newcastle upon Tyne to capture stunning images that make your dishes irresistible online."
        }
      ]
    },
    {
      "category": "Technical & Pricing",
      "icon": <BarChart3 className="h-6 w-6" />,
      "questions": [
        {
          "q": "What are the costs involved in a restaurant website?",
          "a": "Our restaurant website design packages typically range from £3,500 to custom solutions. Pricing depends on features, complexity, and ongoing support needs. We provide transparent quotes."
        },
        {
          "q": "Is SEO included in your restaurant website design service?",
          "a": "Yes, local SEO optimisation is a core component of our service. We ensure your website is structured and optimised to rank highly for relevant search terms in Newcastle and surrounding areas."
        },
        {
          "q": "Do you provide ongoing maintenance and support?",
          "a": "Yes, we offer comprehensive maintenance and support packages to ensure your website remains secure, updated, and performs optimally, allowing you to focus on running your restaurant."
        }
      ]
    }
  ],
  "finalCTA": {
    "badge": "Get Started",
    "title": "Ready to Serve Up a Stunning Online Presence?",
    "description": "Don't let an outdated website hold your Newcastle restaurant back. Partner with WebforLeads to create a digital experience that drives bookings, boosts orders, and truly reflects your culinary excellence. Contact us today for a no-obligation consultation."
  },
  "structuredData": {
    "serviceName": "Restaurant Website Design",
    "description": "WebforLeads offers bespoke restaurant website design services in Newcastle upon Tyne, focusing on conversion-driven, mobile-responsive sites with integrated booking and online ordering systems.",
    "priceRange": "£3500-Custom",
    "areaServed": "Newcastle upon Tyne",
    "aggregateRating": {
      "ratingValue": "4.9",
      "reviewCount": "127"
    }
  }
};

export const metadata: Metadata = {
  title: serviceConfig.metaTitle,
  description: serviceConfig.metaDescription,
  keywords: serviceConfig.keywords.join(", "),
  openGraph: {
    title: serviceConfig.metaTitle,
    description: serviceConfig.metaDescription,
    type: 'website',
    url: `https://webforleads.uk/web-design/${serviceConfig.serviceSlug}`,
  },
  twitter: {
    card: 'summary_large_image',
    title: serviceConfig.metaTitle,
    description: serviceConfig.metaDescription,
  },
  alternates: {
    canonical: `https://webforleads.uk/web-design/${serviceConfig.serviceSlug}`,
  },
};

export default function RestaurantWebsiteDesignNewcastleUponTynePage() {
  return <ServiceTemplate config={serviceConfig} />;
}


