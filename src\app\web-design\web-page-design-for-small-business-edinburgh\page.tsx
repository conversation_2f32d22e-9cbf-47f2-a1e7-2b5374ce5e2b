import { Metadata } from 'next';
import ServiceTemplate from '../../site-pages/service-template';
import {
  Palette, Smartphone, TrendingUp, Search, Brain, Gauge,
  Target, Users, Rocket, BarChart3, Globe, Zap, Code,
  DollarSign, MousePointer, Share2, Heart, MessageCircle,
  Camera, Tablet, Monitor
} from "lucide-react";

// Web Page Design for Small Business Service Configuration for web-page-design-for-small-business-edinburgh
// Generated using Web Design Services template for WebforLeads
const serviceConfig = {
  "serviceName": "Web Page Design for Small Business",
  "serviceSlug": "web-page-design-for-small-business-edinburgh",
  "metaTitle": "Web Page Design for Small Business Edinburgh | WebforLeads UK",
  "metaDescription": "Boost your Edinburgh small business with professional web page design from WebforLeads. We craft conversion-focused websites that attract local customers and drive growth across Scotland.",
  "keywords": [
    "web page design edinburgh",
    "small business website edinburgh",
    "local web design scotland",
    "edinburgh web development",
    "affordable web design edinburgh",
    "website builder edinburgh",
    "digital marketing edinburgh",  ],
  "heroTitle": "Elevate Your Edinburgh Small Business with Strategic Web Page Design",
  "heroSubtitle": "Crafting High-Converting Websites for Local Success",
  "heroDescription": "At WebforLeads, we specialise in creating bespoke web pages that don't just look good, but perform exceptionally. For small businesses across Edinburgh, from Leith to Morningside, our data-driven approach ensures your online presence captures attention, engages visitors, and converts them into loyal customers. We understand the unique market dynamics of Scotland's capital.",
  "heroBadgeText": "EDINBURGH'S WEB DESIGN EXPERTS",
  "stats": [
    {
      "number": "340%",
      "label": "Average ROI for Clients",
      "icon": <TrendingUp className="h-6 w-6" />
    },
    {
      "number": "£1.8M+",
      "label": "Client Revenue Generated",
      "icon": <Target className="h-6 w-6" />
    },
    {
      "number": "96%",
      "label": "Client Satisfaction Rate",
      "icon": <Users className="h-6 w-6" />
    },
    {
      "number": "24hrs",
      "label": "Initial Concept Delivery",
      "icon": <Rocket className="h-6 w-6" />
    }
  ],
  "designFeatures": [
    {
      "icon": <Brain className="h-8 w-8" />,
      "title": "Strategic User Experience (UX)",
      "description": "We design intuitive navigation and engaging layouts that guide your Edinburgh customers seamlessly through your site, ensuring a positive first impression.",
      "highlight": "Intuitive & Engaging"
    },
    {
      "icon": <Gauge className="h-8 w-8" />,
      "title": "Optimised for Conversion",
      "description": "Every element is strategically placed to encourage action, whether it's a booking for your Royal Mile tour or an enquiry for your New Town consultancy.",
      "highlight": "Action-Oriented"
    },
    {
      "icon": <Target className="h-6 w-6" />,
      "title": "Mobile-First Responsiveness",
      "description": "Your web page will look flawless and function perfectly on any device, from smartphones to desktops, crucial for reaching customers on the go in Edinburgh.",
      "highlight": "Seamless Across Devices"
    },
    {
      "icon": <BarChart3 className="h-6 w-6" />,
      "title": "SEO-Ready Foundations",
      "description": "Built with search engine best practices in mind, your site will be primed to rank higher for local Edinburgh searches, driving organic traffic.",
      "highlight": "Visibility & Reach"
    }
  ],
  "features": [
    {
      "icon": <Search className="h-6 w-6" />,
      "title": "Local SEO Integration",
      "description": "Ensuring your business is found by customers searching for services in Edinburgh, Portobello, or Stockbridge."
    },
    {
      "icon": <Target className="h-6 w-6" />,
      "title": "Conversion Rate Optimisation (CRO)",
      "description": "Designing pages that turn visitors into leads and sales, maximising your return on investment."
    },
    {
      "icon": <Globe className="h-6 w-6" />,
      "title": "Custom Web Development",
      "description": "Tailored solutions that reflect your unique brand identity and business goals, not just a template."
    },
    {
      "icon": <Zap className="h-6 w-6" />,
      "title": "Blazing Fast Load Times",
      "description": "Optimised performance for an excellent user experience, reducing bounce rates and improving SEO."
    },
    {
      "icon": <BarChart3 className="h-6 w-6" />,
      "title": "Analytics & Reporting",
      "description": "Transparent insights into your website's performance, helping you understand your Edinburgh audience."
    },
    {
      "icon": <Users className="h-6 w-6" />,
      "title": "Dedicated Project Manager",
      "description": "A single point of contact to guide your Edinburgh web design project from concept to launch."
    }
  ],
  "packages": [
    {
      "name": "Starter Edinburgh Site",
      "price": "£3,500",
      "period": "one-time",
      "description": "Ideal for new Edinburgh businesses needing a professional online presence quickly.",
      "features": [
        "5-Page Custom Design",
        "Mobile Responsive",
        "Basic SEO Setup",
        "Contact Form Integration",      ],
      "highlight": false,
      "cta": "Get Started"
    },
    {
      "name": "Growth Edinburgh Package",
      "price": "£7,500",
      "period": "one-time",
      "description": "Designed for growing Edinburgh businesses seeking advanced features and stronger lead generation.",
      "features": [
        "Up to 15-Page Custom Design",
        "Advanced SEO Optimisation",
        "CRM Integration",
        "Blog/News Section",
        "E-commerce Ready (up to 10 products)",      ],
      "highlight": true,
      "cta": "Boost My Business"
    },
    {
      "name": "Enterprise Edinburgh Solution",
      "price": "Custom",
      "period": "one-time",
      "description": "For established Edinburgh enterprises requiring complex functionalities and bespoke integrations.",
      "features": [
        "Unlimited Custom Pages",
        "Advanced E-commerce Solutions",
        "Custom API Integrations",
        "Ongoing CRO & A/B Testing",
        "Dedicated Support & Training",      ],
      "highlight": false,
      "cta": "Request Consultation"
    }
  ],
  "process": [
    {
      "step": "01",
      "title": "Discovery & Strategy",
      "description": "We begin by understanding your Edinburgh business, target audience, and competitive landscape. This forms the blueprint for your conversion-focused web page.",
      "duration": "Week 1"
    },
    {
      "step": "02",
      "title": "Design & Development",
      "description": "Our expert designers and developers craft a visually stunning and highly functional website, incorporating your brand identity and user experience best practices.",
      "duration": "Week 2-3"
    },
    {
      "step": "03",
      "title": "Content & Optimisation",
      "description": "We integrate compelling, SEO-optimised content and ensure all technical elements are fine-tuned for performance, speed, and search engine visibility in Edinburgh.",
      "duration": "Week 4-5"
    },
    {
      "step": "04",
      "title": "Launch & Support",
      "description": "After rigorous testing, your new web page goes live. We provide ongoing support and analytics to ensure sustained growth and success for your Edinburgh business.",
      "duration": "Week 6+"
    }
  ],
  "featuredTestimonial": {
    "quote": "WebforLeads transformed our online presence. Our new website is not only beautiful but has significantly increased our online bookings from customers across Edinburgh and beyond. The team truly understood our vision.",
    "name": "Sarah Jenkins",
    "role": "Owner",
    "company": "The Royal Mile Coffee Co.",
    "rating": 5,
    "result": "280%",
    "resultLabel": "Increase in Online Bookings"
  },
  "testimonials": [
    {
      "quote": "Our old site was struggling to attract local clients in Leith. WebforLeads built us a modern, mobile-friendly web page that now consistently brings in new enquiries. Fantastic results!",
      "name": "Mark Campbell",
      "role": "Director",
      "company": "Leith Logistics Solutions",
      "industry": "Logistics",
      "metric": "315%",
      "metricLabel": "Increase in Local Enquiries",
      "avatar": "SES",    },
    {
      "quote": "As a small boutique in Morningside, we needed a website that reflected our brand's elegance and was easy for customers to navigate. WebforLeads delivered beyond our expectations, boosting our online sales.",
      "name": "Eleanor Reid",
      "role": "Founder",
      "company": "Morningside Boutique",
      "industry": "Retail",
      "metric": "200%",
      "metricLabel": "Growth in Online Sales",
      "avatar": "ER",    },
    {
      "quote": "The team at WebforLeads are true professionals. They understood our specific needs as a financial consultancy in Edinburgh's New Town and delivered a high-performing website that generates quality leads.",
      "name": "David Paterson",
      "role": "Managing Partner",
      "company": "Caledonian Capital Advisors",
      "industry": "Finance",
      "metric": "400%",
      "metricLabel": "Improvement in Lead Quality",
      "avatar": "DP",    }
  ],
  "faqCategories": [
    {
      "category": "Process & Timeline",
      "icon": <Rocket className="h-6 w-6" />,
      "questions": [
        {
          "q": "How long does it take to design a web page for a small business?",
          "a": "Typically, a standard small business web page design project with WebforLeads takes between 6-8 weeks from initial consultation to launch, depending on complexity and content readiness. We work efficiently to get your Edinburgh business online quickly."
        },
        {
          "q": "Can I see progress during the design process?",
          "a": "Absolutely. We provide regular updates and access to a staging environment where you can review and provide feedback on your web page design at key milestones, ensuring it aligns with your vision for your Edinburgh business."
        },
        {
          "q": "What if I need changes after the website is launched?",
          "a": "We offer post-launch support and maintenance packages to ensure your web page remains updated and secure. Minor content updates are often included in our initial support period, and we're always here for larger modifications."
        }
      ]
    },
    {
      "category": "Services & Features",
      "icon": <Target className="h-6 w-6" />,
      "questions": [
        {
          "q": "Do you provide content writing for the web page?",
          "a": "Yes, we offer professional content writing services, including SEO-optimised copy tailored to your Edinburgh business and target audience, ensuring your message is clear and compelling."
        },
        {
          "q": "Will my web page be mobile-friendly?",
          "a": "Every web page we design is built with a mobile-first approach, ensuring it is fully responsive and provides an optimal viewing experience across all devices, from smartphones to desktops, crucial for Edinburgh's diverse audience."
        },
        {
          "q": "Can you integrate e-commerce functionality?",
          "a": "Yes, we specialise in creating secure and user-friendly e-commerce solutions, enabling your Edinburgh business to sell products or services directly from your new web page, complete with payment gateway integration."
        }
      ]
    },
    {
      "category": "Technical & Pricing",
      "icon": <BarChart3 className="h-6 w-6" />,
      "questions": [
        {
          "q": "What is the typical cost for a small business web page design in Edinburgh?",
          "a": "Our web page design packages for small businesses typically range from £3,500 to £15,000+, depending on the complexity, features, and customisation required. We provide transparent, tailored quotes after understanding your specific needs."
        },
        {
          "q": "Do you offer hosting and domain registration?",
          "a": "Yes, we can assist with both domain registration and provide reliable, secure hosting solutions for your new web page, ensuring your Edinburgh business stays online and performs optimally."
        },
        {
          "q": "Is SEO included in your web page design services?",
          "a": "Yes, fundamental SEO best practices are integrated into every web page we build, including keyword research, on-page optimisation, and technical SEO setup, to give your Edinburgh business a strong foundation for online visibility."
        }
      ]
    }
  ],
  "finalCTA": {
    "badge": "Get Started",
    "title": "Ready to Transform Your Edinburgh Business Online?",
    "description": "Don't let an outdated or non-existent web page hold your small business back. Partner with WebforLeads, Edinburgh's trusted digital marketing agency, to create a powerful online presence that drives conversions and growth. Contact us today for a no-obligation consultation."
  },
  "structuredData": {
    "serviceName": "Web Page Design for Small Business",
    "description": "Professional web page design services for small businesses in Edinburgh, UK, focusing on conversion-driven, mobile-responsive, and SEO-optimised websites.",
    "priceRange": "£3500-£15000",
    "areaServed": "Edinburgh",
    "aggregateRating": {
      "ratingValue": "4.9",
      "reviewCount": "127"
    }
  }
};

export const metadata: Metadata = {
  title: serviceConfig.metaTitle,
  description: serviceConfig.metaDescription,
  keywords: serviceConfig.keywords.join(", "),
  openGraph: {
    title: serviceConfig.metaTitle,
    description: serviceConfig.metaDescription,
    type: 'website',
    url: `https://webforleads.uk/web-design/${serviceConfig.serviceSlug}`,
  },
  twitter: {
    card: 'summary_large_image',
    title: serviceConfig.metaTitle,
    description: serviceConfig.metaDescription,
  },
  alternates: {
    canonical: `https://webforleads.uk/web-design/${serviceConfig.serviceSlug}`,
  },
};

export default function WebPageDesignForSmallBusinessEdinburghPage() {
  return <ServiceTemplate config={serviceConfig} />;
}


