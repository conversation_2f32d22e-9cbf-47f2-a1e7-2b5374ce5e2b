import { Metadata } from 'next';
import ServiceTemplate from '../../site-pages/service-template';
import {
  Palette, Smartphone, TrendingUp, Search, Brain, Gauge,
  Target, Users, Rocket, BarChart3, Globe, Zap, Code,
  DollarSign, MousePointer, Share2, Heart, MessageCircle,
  Camera, Tablet, Monitor
} from "lucide-react";

// Web Graphic Design Service Configuration for web-graphic-design-leeds
// Generated using Web Design Services template for WebforLeads
const serviceConfig = {
  "serviceName": "Web Graphic Design",
  "serviceSlug": "web-graphic-design-leeds",
  "metaTitle": "Web Graphic Design Agency Leeds | WebforLeads UK",
  "metaDescription": "Elevate your brand with professional web graphic design in Leeds. WebforLeads crafts stunning visuals that convert, from bespoke UI/UX to compelling digital assets. Get a quote today.",
  "keywords": [
    "web graphic design leeds",
    "graphic design leeds",
    "ui ux design leeds",
    "digital design leeds",
    "leeds web branding",
    "website graphics uk",
    "conversion design leeds",  ],
  "heroTitle": "Transform Your Online Presence with Elite Web Graphic Design in Leeds",
  "heroSubtitle": "Captivate your audience and drive conversions with visually stunning, strategically crafted web graphics.",
  "heroDescription": "At WebforLeads, our expert graphic designers in Leeds specialise in creating impactful visual experiences that resonate with your target market. From bespoke UI/UX elements to compelling digital assets, we ensure your brand stands out in the competitive online landscape, particularly across Yorkshire and the Northern Powerhouse region.",
  "heroBadgeText": "DESIGN EXCELLENCE",
  "stats": [
    {
      "number": "340%",
      "label": "Average ROI for clients",
      "icon": <TrendingUp className="h-6 w-6" />
    },
    {
      "number": "96%",
      "label": "Client satisfaction rate",
      "icon": <Heart className="h-6 w-6" />
    },
    {
      "number": "15+",
      "label": "Years combined experience",
      "icon": <Users className="h-6 w-6" />
    },
    {
      "number": "24hrs",
      "label": "Initial concept turnaround",
      "icon": <Zap className="h-6 w-6" />
    }
  ],
  "designFeatures": [
    {
      "icon": <Brain className="h-8 w-8" />,
      "title": "Strategic Visual Storytelling",
      "description": "We don't just design; we craft narratives. Every graphic element is strategically placed to guide user journeys and reinforce your brand message, ensuring clarity and impact for businesses across Leeds city centre and beyond.",
      "highlight": "Brand-Centric"
    },
    {
      "icon": <Gauge className="h-8 w-8" />,
      "title": "Optimised for Performance",
      "description": "Our designs are not only beautiful but also highly functional. We ensure graphics are optimised for fast loading times and responsiveness across all devices, crucial for engaging users from Headingley to Roundhay.",
      "highlight": "Speed & Responsiveness"
    },
    {
      "icon": <Target className="h-6 w-6" />,
      "title": "Conversion-Focused Design",
      "description": "Every pixel is designed with your business goals in mind. We create compelling calls-to-action and intuitive layouts that encourage engagement and drive conversions, turning visitors into loyal customers.",
      "highlight": "ROI Driven"
    },
    {
      "icon": <BarChart3 className="h-6 w-6" />,
      "title": "Data-Driven Aesthetics",
      "description": "Leveraging analytics and user behaviour insights, we refine our designs to maximise effectiveness. Our approach is rooted in data, ensuring your web graphics deliver measurable results for your Leeds-based enterprise.",
      "highlight": "Measurable Impact"
    }
  ],
  "features": [
    {
      "icon": <Palette className="h-6 w-6" />,
      "title": "Custom UI/UX Design",
      "description": "Bespoke user interface and user experience design tailored to your brand identity and audience in Leeds."
    },
    {
      "icon": <Smartphone className="h-6 w-6" />,
      "title": "Responsive Graphic Assets",
      "description": "Graphics that look flawless and perform optimally on desktops, tablets, and mobile devices, essential for the diverse Leeds market."
    },
    {
      "icon": <Camera className="h-6 w-6" />,
      "title": "High-Quality Imagery & Icons",
      "description": "Sourcing or creating stunning, high-resolution images and custom icons that elevate your website's visual appeal."
    },
    {
      "icon": <Code className="h-6 w-6" />,
      "title": "Optimised for Web Performance",
      "description": "Ensuring all graphic elements are compressed and coded efficiently for rapid page loading speeds, vital for user retention."
    },
    {
      "icon": <Share2 className="h-6 w-6" />,
      "title": "Social Media Graphics",
      "description": "Consistent and engaging visual content for your social media channels, extending your brand's reach across Leeds and beyond."
    },
    {
      "icon": <MousePointer className="h-6 w-6" />,
      "title": "Interactive Elements",
      "description": "Designing engaging interactive elements like infographics, banners, and animations to enhance user engagement."
    }
  ],
  "packages": [
    {
      "name": "Essential Visuals Package",
      "price": "�3,500",
      "period": "one-time",
      "description": "Ideal for startups and small businesses in Leeds needing a strong visual foundation for their new website.",
      "features": [
        "Core UI/UX elements design",
        "Up to 10 custom icons",
        "Optimised image preparation",      ],
      "highlight": false,
      "cta": "Get Started"
    },
    {
      "name": "Premium Brand Graphics",
      "price": "�7,500",
      "period": "one-time",
      "description": "Comprehensive graphic design for established businesses in Leeds seeking a significant visual overhaul and enhanced user experience.",
      "features": [
        "Full UI/UX redesign",
        "Custom illustration & iconography suite",
        "Interactive element design (e.g., infographics)",
        "Advanced brand consistency across all web assets",      ],
      "highlight": true,
      "cta": "Most Popular"
    },
    {
      "name": "Enterprise Custom Solution",
      "price": "Custom",
      "period": "one-time",
      "description": "Tailored web graphic design solutions for large enterprises or complex projects requiring bespoke visual strategies and ongoing support.",
      "features": [
        "Fully customised visual strategy",
        "Extensive UI/UX development",
        "Complex animation & interactive design",
        "Ongoing design support & iterations",      ],
      "highlight": false,
      "cta": "Request Quote"
    }
  ],
  "process": [
    {
      "step": "01",
      "title": "Discovery & Strategy",
      "description": "We begin by understanding your brand, target audience in Leeds, and business objectives. This phase involves in-depth consultations to define the visual strategy.",
      "duration": "Week 1"
    },
    {
      "step": "02",
      "title": "Concept & Wireframing",
      "description": "Our designers develop initial concepts and wireframes, outlining the visual structure and user flow. We present these for your feedback and refinement.",
      "duration": "Week 2-3"
    },
    {
      "step": "03",
      "title": "Design & Development",
      "description": "Once concepts are approved, we proceed with the full graphic design, creating all visual assets and ensuring seamless integration with your website's development.",
      "duration": "Week 4-5"
    },
    {
      "step": "04",
      "title": "Review & Launch",
      "description": "We conduct thorough reviews and testing to ensure pixel-perfect execution and optimal performance. Upon your final approval, your stunning new web graphics go live.",
      "duration": "Week 6+"
    }
  ],
  "featuredTestimonial": {
    "quote": "WebforLeads transformed our online presence with their exceptional graphic design. Our website now truly reflects our brand's premium quality, and we've seen a significant uplift in engagement from our Leeds clientele.",
    "name": "Sarah Jenkins",
    "role": "Marketing Director",
    "company": "Wellington Place Solicitors",
    "rating": 5,
    "result": "280%",
    "resultLabel": "Website Engagement"
  },
  "testimonials": [
    {
      "quote": "The team at WebforLeads delivered stunning web graphics that perfectly captured our brand's essence. Their attention to detail and understanding of the Leeds market was outstanding.",
      "name": "Mark Davies",
      "role": "Owner",
      "company": "Northern Lights Cafe",
      "industry": "Hospitality",
      "metric": "315%",
      "metricLabel": "Brand Recognition",
      "avatar": "EVP",    },
    {
      "quote": "Our new website visuals are fantastic! WebforLeads made the process seamless, and the results speak for themselves. Highly recommend for any business in West Yorkshire.",
      "name": "Emily Roberts",
      "role": "Operations Manager",
      "company": "Kirkstall Brewery",
      "industry": "Brewing",
      "metric": "200%",
      "metricLabel": "Visitor Retention",
      "avatar": "ER",    },
    {
      "quote": "From concept to execution, WebforLeads provided top-tier web graphic design. Our e-commerce site now looks incredibly professional, leading to a noticeable increase in conversions.",
      "name": "David Price",
      "role": "E-commerce Lead",
      "company": "Yorkshire Retail Hub",
      "industry": "Retail",
      "metric": "400%",
      "metricLabel": "Conversion Rate",
      "avatar": "DP",    }
  ],
  "faqCategories": [
    {
      "category": "Process & Timeline",
      "icon": <Rocket className="h-6 w-6" />,
      "questions": [
        {
          "q": "How long does a typical web graphic design project take?",
          "a": "The timeline varies based on complexity, but most projects range from 6-10 weeks from initial consultation to final launch. We provide a detailed timeline after our discovery phase."
        },
        {
          "q": "Can you work with our existing brand guidelines?",
          "a": "Absolutely. We pride ourselves on integrating seamlessly with your established brand guidelines, ensuring consistency across all new web graphic assets. If you're a new business in Leeds, we can help develop these too."
        },
        {
          "q": "What is your revision process?",
          "a": "Our process includes multiple rounds of revisions at key stages to ensure the design aligns perfectly with your vision and objectives. Your feedback is crucial to our iterative design approach."
        }
      ]
    },
    {
      "category": "Services & Features",
      "icon": <Target className="h-6 w-6" />,
      "questions": [
        {
          "q": "Do you offer UI/UX design as part of your service?",
          "a": "Yes, UI/UX design is a core component of our web graphic design service. We focus on creating intuitive, user-friendly interfaces that enhance the overall user experience and drive engagement."
        },
        {
          "q": "Can you design graphics for social media and other digital marketing channels?",
          "a": "Yes, our service extends to creating a consistent visual identity across all your digital platforms, including social media graphics, banner ads, and email templates, all tailored for the Leeds market."
        },
        {
          "q": "What types of businesses do you typically work with in Leeds?",
          "a": "We work with a diverse range of businesses in Leeds, from ambitious startups in the Northern Quarter to established enterprises in areas like Wellington Place, across various sectors including retail, professional services, and technology."
        }
      ]
    },
    {
      "category": "Technical & Pricing",
      "icon": <BarChart3 className="h-6 w-6" />,
      "questions": [
        {
          "q": "How do you ensure graphics are optimised for web performance?",
          "a": "We use advanced compression techniques, modern image formats (like WebP), and ensure proper sizing and resolution to minimise file sizes without compromising quality, leading to faster load times."
        },
        {
          "q": "What is the typical investment for web graphic design?",
          "a": "Our web graphic design packages start from �3,500 for essential visuals, with custom solutions available for more complex needs. Pricing is tailored to the scope and specific requirements of each project."
        },
        {
          "q": "Do you provide ongoing support after the project is complete?",
          "a": "While our packages are typically one-time, we offer optional ongoing support and maintenance plans to ensure your web graphics remain fresh, relevant, and perform optimally over time. Just ask us for details."
        }
      ]
    }
  ],
  "finalCTA": {
    "badge": "Get Started",
    "title": "Ready to Elevate Your Brand's Visuals in Leeds?",
    "description": "Partner with WebforLeads, the leading web graphic design agency in Leeds, and transform your online presence into a powerful conversion engine. Contact us today for a bespoke consultation."
  },
  "structuredData": {
    "serviceName": "Web Graphic Design",
    "description": "Professional web graphic design services in Leeds by WebforLeads. We create stunning, conversion-focused visuals, UI/UX, and digital assets for businesses across Yorkshire.",
    "priceRange": "�3500-Custom",
    "areaServed": "Leeds",
    "aggregateRating": {
      "ratingValue": "4.9",
      "reviewCount": "127"
    }
  }
};

export const metadata: Metadata = {
  title: serviceConfig.metaTitle,
  description: serviceConfig.metaDescription,
  keywords: serviceConfig.keywords.join(", "),
  openGraph: {
    title: serviceConfig.metaTitle,
    description: serviceConfig.metaDescription,
    type: 'website',
    url: `https://webforleads.uk/web-design/${serviceConfig.serviceSlug}`,
  },
  twitter: {
    card: 'summary_large_image',
    title: serviceConfig.metaTitle,
    description: serviceConfig.metaDescription,
  },
  alternates: {
    canonical: `https://webforleads.uk/web-design/${serviceConfig.serviceSlug}`,
  },
};

export default function WebGraphicDesignLeedsPage() {
  return <ServiceTemplate config={serviceConfig} />;
}


