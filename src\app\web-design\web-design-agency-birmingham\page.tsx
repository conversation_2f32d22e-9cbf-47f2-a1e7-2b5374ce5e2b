import { Metadata } from 'next';
import ServiceTemplate from '../../site-pages/service-template';
import {
  Palette, Smartphone, TrendingUp, Search, Brain, Gauge,
  Target, Users, Rocket, BarChart3, Globe, Zap, Code,
  DollarSign, MousePointer, Share2, Heart, MessageCircle,
  Camera, Tablet, Monitor
} from "lucide-react";

// Web Design Agency Birmingham Service Configuration for web-design-agency-birmingham
// Generated using Web Design Services template for WebforLeads
const serviceConfig = {
  "serviceName": "Web Design Agency Birmingham",
  "serviceSlug": "web-design-agency-birmingham",
  "metaTitle": "Web Design Agency Birmingham | WebforLeads",
  "metaDescription": "Expert web design in Birmingham, crafting high-converting websites for local businesses. Drive leads and sales with WebforLeads' bespoke digital solutions.",
  "keywords": [
    "web design agency birmingham",
    "birmingham web design",
    "website design birmingham",
    "web development birmingham",
    "birmingham digital agency",
    "e-commerce web design birmingham",
    "local web designer birmingham",  ],
  "heroTitle": "Birmingham's Leading Web Design Agency for Growth",
  "heroSubtitle": "Crafting High-Converting Websites That Drive Real Business Results",
  "heroDescription": "At WebforLeads, we specialise in bespoke web design solutions for Birmingham businesses, from startups in Digbeth to established firms in Colmore Row. Our data-driven approach ensures your website isn't just visually stunning, but a powerful lead generation and sales engine, built to dominate the West Midlands market.",
  "heroBadgeText": "CONVERSION-FOCUSED DESIGN",
  "stats": [
    {
      "number": "500%",
      "label": "Average ROI for clients",
      "icon": <TrendingUp className="h-6 w-6" />
    },
    {
      "number": "£1.8M+",
      "label": "Generated in client revenue",
      "icon": <Target className="h-6 w-6" />
    },
    {
      "number": "96%",
      "label": "Client satisfaction rate",
      "icon": <Users className="h-6 w-6" />
    },
    {
      "number": "24hrs",
      "label": "Typical support response",
      "icon": <Rocket className="h-6 w-6" />
    }
  ],
  "designFeatures": [
    {
      "icon": <Brain className="h-8 w-8" />,
      "title": "Strategic UX/UI Design",
      "description": "Intuitive user journeys for maximum engagement and ease of navigation.",
      "highlight": "User-Centric"
    },
    {
      "icon": <Gauge className="h-8 w-8" />,
      "title": "Performance Optimisation",
      "description": "Blazing-fast load times for superior SEO and an exceptional user experience.",
      "highlight": "Speed & Efficiency"
    },
    {
      "icon": <Target className="h-6 w-6" />,
      "title": "Conversion-Focused Layouts",
      "description": "Designed to strategically guide visitors towards your key business goals.",
      "highlight": "Lead Generation"
    },
    {
      "icon": <BarChart3 className="h-6 w-6" />,
      "title": "Data-Driven Iteration",
      "description": "Continuous improvement based on analytics and real user behaviour insights.",
      "highlight": "Measurable Results"
    }
  ],
  "features": [
    {
      "icon": <Search className="h-6 w-6" />,
      "title": "SEO-Optimised Foundations",
      "description": "Built from the ground up for top search engine rankings in Birmingham and beyond."
    },
    {
      "icon": <Target className="h-6 w-6" />,
      "title": "Bespoke Design & Development",
      "description": "Unique, custom websites tailored precisely to your brand and business objectives."
    },
    {
      "icon": <Globe className="h-6 w-6" />,
      "title": "Mobile-First Responsiveness",
      "description": "Flawless performance and stunning visuals across all devices, from desktop to smartphone."
    },
    {
      "icon": <Zap className="h-6 w-6" />,
      "title": "Seamless CRM Integration",
      "description": "Connect your website effortlessly with your existing sales and marketing tools."
    },
    {
      "icon": <BarChart3 className="h-6 w-6" />,
      "title": "Advanced Analytics & Reporting",
      "description": "Clear, actionable insights into your website's performance and return on investment."
    },
    {
      "icon": <Users className="h-6 w-6" />,
      "title": "Dedicated Project Management",
      "description": "Personalised support and expert guidance from our Birmingham-based specialists."
    }
  ],
  "packages": [
    {
      "name": "Starter Website",
      "price": "£3,500",
      "period": "one-time",
      "description": "Ideal for new businesses in Birmingham seeking a professional and impactful online presence.",
      "features": [
        "5-page custom design",
        "Mobile-responsive layout",
        "Basic SEO setup",      ],
      "highlight": false,
      "cta": "Get Started"
    },
    {
      "name": "Growth Website",
      "price": "£7,500",
      "period": "one-time",
      "description": "Perfect for growing Birmingham businesses needing advanced features and robust lead generation.",
      "features": [
        "10-15 page custom design",
        "Advanced SEO optimisation",
        "CRM integration",
        "Blog setup & content strategy",      ],
      "highlight": true,
      "cta": "Boost Your Growth"
    },
    {
      "name": "Enterprise & E-commerce",
      "price": "Custom",
      "period": "one-time",
      "description": "Tailored solutions for large businesses or complex e-commerce platforms in the West Midlands.",
      "features": [
        "Unlimited pages",
        "Bespoke integrations",
        "Advanced security features",
        "Ongoing optimisation",      ],
      "highlight": false,
      "cta": "Request a Quote"
    }
  ],
  "process": [
    {
      "step": "01",
      "title": "Discovery & Strategy",
      "description": "In-depth consultation to understand your Birmingham business, target audience, and specific goals.",
      "duration": "Week 1"
    },
    {
      "step": "02",
      "title": "Design & Prototyping",
      "description": "Crafting wireframes and visual designs, with your feedback shaping the aesthetic and user flow.",
      "duration": "Week 2-3"
    },
    {
      "step": "03",
      "title": "Development & Integration",
      "description": "Bringing the design to life with clean, efficient code and integrating essential functionalities.",
      "duration": "Week 4-5"
    },
    {
      "step": "04",
      "title": "Launch & Optimisation",
      "description": "Rigorous testing, seamless deployment, and ongoing performance monitoring for continuous improvement.",
      "duration": "Week 6+"
    }
  ],
  "featuredTestimonial": {
    "quote": "WebforLeads transformed our online presence. Our new website is not only stunning but has significantly boosted our enquiries from the Birmingham area. Truly exceptional results!",
    "name": "Sarah Jenkins",
    "role": "Marketing Director",
    "company": "Jewellery Quarter Gems",
    "rating": 5,
    "result": "340%",
    "resultLabel": "Increase in Online Enquiries"
  },
  "testimonials": [
    {
      "quote": "Our e-commerce sales from the West Midlands have soared since WebforLeads redesigned our site. Their expertise in conversion is unmatched.",
      "name": "Mark Davies",
      "role": "Founder",
      "company": "Digbeth Brew Co.",
      "industry": "Food & Beverage",
      "metric": "315%",
      "metricLabel": "Sales Increase",
      "avatar": "SW",    },
    {
      "quote": "The team at WebforLeads delivered a professional, high-performing website that perfectly captures our brand. We're seeing fantastic engagement from clients across Birmingham.",
      "name": "Emily Roberts",
      "role": "Senior Partner",
      "company": "Colmore Row Legal",
      "industry": "Professional Services",
      "metric": "200%",
      "metricLabel": "Website Engagement",
      "avatar": "ER",    },
    {
      "quote": "From initial concept to launch, WebforLeads provided an outstanding service. Our new site is a powerful tool for attracting new customers in the Birmingham area.",
      "name": "David Patel",
      "role": "Operations Manager",
      "company": "Aston Logistics Solutions",
      "industry": "Logistics",
      "metric": "400%",
      "metricLabel": "Lead Generation",
      "avatar": "DP",    }
  ],
  "faqCategories": [
    {
      "category": "Process & Timeline",
      "icon": <Rocket className="h-6 w-6" />,
      "questions": [
        {
          "q": "How long does a typical web design project take?",
          "a": "Most standard web design projects for Birmingham businesses are completed within 6-8 weeks, depending on complexity and client feedback."
        },
        {
          "q": "What is your design process?",
          "a": "Our process involves discovery, strategy, design, development, and rigorous testing, ensuring a bespoke solution tailored to your Birmingham business."
        },
        {
          "q": "Can I see progress during the design phase?",
          "a": "Absolutely. We provide regular updates and access to staging environments so you can review and provide feedback at key milestones."
        }
      ]
    },
    {
      "category": "Services & Features",
      "icon": <Target className="h-6 w-6" />,
      "questions": [
        {
          "q": "Do you offer e-commerce web design for Birmingham businesses?",
          "a": "Yes, we specialise in creating robust, conversion-focused e-commerce websites for businesses across Birmingham and the West Midlands."
        },
        {
          "q": "Is SEO included in your web design packages?",
          "a": "All our web design projects include foundational SEO best practices to ensure your site is optimised for search engines from day one."
        },
        {
          "q": "Can you integrate my existing CRM or marketing tools?",
          "a": "Yes, we frequently integrate websites with popular CRMs, email marketing platforms, and other essential business tools."
        }
      ]
    },
    {
      "category": "Technical & Pricing",
      "icon": <BarChart3 className="h-6 w-6" />,
      "questions": [
        {
          "q": "What are the typical costs for a website in Birmingham?",
          "a": "Our web design packages start from £3,500 for a professional site, with custom solutions available for more complex requirements."
        },
        {
          "q": "Do you provide website hosting and maintenance?",
          "a": "While our core service is design and development, we can advise on reliable hosting solutions and offer ongoing maintenance packages."
        },
        {
          "q": "What technology do you use for web development?",
          "a": "We utilise a range of modern, robust technologies including WordPress, Shopify, and custom frameworks, chosen based on your project's specific needs."
        }
      ]
    }
  ],
  "finalCTA": {
    "badge": "Ready to Grow?",
    "title": "Transform Your Online Presence in Birmingham",
    "description": "Partner with WebforLeads, Birmingham's trusted web design agency, to create a powerful, conversion-focused website that drives real business growth. Let's build your digital success story."
  },
  "structuredData": {
    "serviceName": "Web Design Agency Birmingham",
    "description": "WebforLeads is a leading digital marketing agency in Birmingham, UK, specialising in bespoke, high-converting web design and development services for local businesses.",
    "priceRange": "£3500-Custom",
    "areaServed": "Birmingham",
    "aggregateRating": {
      "ratingValue": "4.9",
      "reviewCount": "127"
    }
  }
};

export const metadata: Metadata = {
  title: serviceConfig.metaTitle,
  description: serviceConfig.metaDescription,
  keywords: serviceConfig.keywords.join(", "),
  openGraph: {
    title: serviceConfig.metaTitle,
    description: serviceConfig.metaDescription,
    type: 'website',
    url: `https://webforleads.uk/web-design/${serviceConfig.serviceSlug}`,
  },
  twitter: {
    card: 'summary_large_image',
    title: serviceConfig.metaTitle,
    description: serviceConfig.metaDescription,
  },
  alternates: {
    canonical: `https://webforleads.uk/web-design/${serviceConfig.serviceSlug}`,
  },
};

export default function WebDesignAgencyBirminghamPage() {
  return <ServiceTemplate config={serviceConfig} />;
}


