import { NextRequest, NextResponse } from 'next/server';
import { googleIndexingService } from '@/lib/google-indexing';

export async function POST(request: NextRequest) {
  try {
    console.log('🔄 Starting bulk URL indexing submission...');
    
    const body = await request.json();
    const { urls, type = 'URL_UPDATED' } = body;

    // Validate input
    if (!urls || !Array.isArray(urls) || urls.length === 0) {
      console.log('❌ Missing or invalid URLs in request');
      return NextResponse.json(
        { success: false, error: 'URLs array is required and must not be empty' },
        { status: 400 }
      );
    }

    if (type !== 'URL_UPDATED' && type !== 'URL_DELETED') {
      console.log('❌ Invalid type in request:', type);
      return NextResponse.json(
        { success: false, error: 'Type must be URL_UPDATED or URL_DELETED' },
        { status: 400 }
      );
    }

    // Limit batch size to prevent quota exhaustion
    const maxBatchSize = 50;
    if (urls.length > maxBatchSize) {
      console.log(`❌ Batch size too large: ${urls.length} (max: ${maxBatchSize})`);
      return NextResponse.json(
        { 
          success: false, 
          error: `Batch size too large. Maximum ${maxBatchSize} URLs per request.`,
          maxBatchSize 
        },
        { status: 400 }
      );
    }

    console.log(`📝 Processing ${urls.length} URLs`);
    console.log(`📝 Type: ${type}`);
    console.log(`📝 URLs:`, urls.slice(0, 5).map(url => `  - ${url}`).join('\n'));
    if (urls.length > 5) {
      console.log(`📝 ... and ${urls.length - 5} more URLs`);
    }

    // Submit URLs to Google Indexing API
    const result = await googleIndexingService.submitBatch(urls, type);

    console.log(`📊 Batch submission completed:`);
    console.log(`   Total: ${result.totalRequests}`);
    console.log(`   Successful: ${result.successfulRequests}`);
    console.log(`   Failed: ${result.failedRequests}`);
    console.log(`   Quota used: ${result.quotaUsed}`);

    return NextResponse.json({
      success: result.success,
      message: `Batch submission completed. ${result.successfulRequests}/${result.totalRequests} URLs submitted successfully.`,
      totalRequests: result.totalRequests,
      successfulRequests: result.successfulRequests,
      failedRequests: result.failedRequests,
      quotaUsed: result.quotaUsed,
      results: result.results,
    });
  } catch (error: any) {
    console.error('❌ Unexpected error in bulk-submit API:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Internal server error',
        details: error.message,
      },
      { status: 500 }
    );
  }
}
