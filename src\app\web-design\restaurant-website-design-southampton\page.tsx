import { Metadata } from 'next';
import ServiceTemplate from '../../site-pages/service-template';
import {
  Palette, Smartphone, TrendingUp, Search, Brain, Gauge,
  Target, Users, Rocket, BarChart3, Globe, Zap, Code,
  DollarSign, MousePointer, Share2, Heart, MessageCircle,
  Camera, Tablet, Monitor
} from "lucide-react";

// Restaurant Website Design Service Configuration for restaurant-website-design-southampton
// Generated using Web Design Services template for WebforLeads
const serviceConfig = {
  "serviceName": "Restaurant Website Design",
  "serviceSlug": "restaurant-website-design-southampton",
  "metaTitle": "Restaurant Website Design Southampton | WebforLeads UK",
  "metaDescription": "Elevate your Southampton restaurant with a stunning, conversion-focused website. WebforLeads crafts bespoke designs that attract diners and boost bookings.",
  "keywords": [
    "restaurant website design southampton",
    "southampton restaurant web design",
    "web design for restaurants hampshire",
    "online ordering system southampton",
    "bespoke restaurant websites uk",
    "restaurant marketing southampton",
    "food website design",  ],
  "heroTitle": "Bespoke Restaurant Website Design in Southampton",
  "heroSubtitle": "Attract More Diners & Boost Bookings Across Hampshire",
  "heroDescription": "WebforLeads specialises in crafting high-performance websites for Southampton's vibrant culinary scene. From the bustling Oxford Street to the charming eateries of Shirley, we build digital platforms that showcase your unique flavour, streamline online orders, and consistently fill your tables.",
  "heroBadgeText": "SOUTHAMPTON EXPERTS",
  "stats": [
    {
      "number": "340%",
      "label": "Average Booking Increase",
      "icon": <TrendingUp className="h-6 w-6" />
    },
    {
      "number": "�1.8M+",
      "label": "Client Revenue Generated",
      "icon": <DollarSign className="h-6 w-6" />
    },
    {
      "number": "96%",
      "label": "Client Retention Rate",
      "icon": <Heart className="h-6 w-6" />
    },
    {
      "number": "24hrs",
      "label": "Initial Concept Delivery",
      "icon": <Zap className="h-6 w-6" />
    }
  ],
  "designFeatures": [
    {
      "icon": <Smartphone className="h-6 w-6" />,
      "title": "Mobile-First Responsiveness",
      "description": "Your restaurant's website will look flawless and function perfectly on any device, from smartphones to large desktop monitors, ensuring a seamless user experience for diners on the go.",
      "highlight": "Optimised for Every Screen"
    },
    {
      "icon": <Palette className="h-6 w-6" />,
      "title": "Stunning Visual Menus",
      "description": "Showcase your culinary creations with high-resolution imagery and intuitive menu layouts that entice customers and make ordering a delight. We capture your unique brand aesthetic.",
      "highlight": "Visually Appetising"
    },
    {
      "icon": <MousePointer className="h-6 w-6" />,
      "title": "Integrated Online Booking & Ordering",
      "description": "Streamline reservations and takeaway orders directly through your website, reducing phone calls and freeing up staff. Seamless integration with popular POS systems.",
      "highlight": "Effortless Transactions"
    },
    {
      "icon": <Search className="h-6 w-6" />,
      "title": "Local SEO Dominance",
      "description": "We optimise your site to rank highly for 'restaurants in Southampton', 'takeaway near me', and other crucial local searches, driving organic footfall and online traffic.",
      "highlight": "Rank Higher Locally"
    }
  ],
  "features": [
    {
      "icon": <Target className="h-6 w-6" />,
      "title": "Conversion-Focused Design",
      "description": "Every element is strategically placed to guide visitors towards booking a table or placing an order, maximising your online revenue."
    },
    {
      "icon": <BarChart3 className="h-6 w-6" />,
      "title": "Advanced Analytics & Reporting",
      "description": "Gain deep insights into customer behaviour, popular menu items, and booking trends to make data-driven decisions for your Southampton establishment."
    },
    {
      "icon": <Code className="h-6 w-6" />,
      "title": "Secure & Scalable Hosting",
      "description": "Your website will be hosted on robust, secure servers, ensuring fast loading times and reliable performance, even during peak hours."
    },
    {
      "icon": <Users className="h-6 w-6" />,
      "title": "User-Friendly Content Management",
      "description": "Easily update menus, promotions, and opening hours yourself with an intuitive content management system, putting you in control."
    },
    {
      "icon": <Share2 className="h-6 w-6" />,
      "title": "Social Media Integration",
      "description": "Seamlessly connect your website with your social media channels to amplify your reach and engage with your Southampton community."
    },
    {
      "icon": <MessageCircle className="h-6 w-6" />,
      "title": "Dedicated UK Support",
      "description": "Our Southampton-based team provides ongoing support and maintenance, ensuring your website always performs at its best."
    }
  ],
  "packages": [
    {
      "name": "Starter Menu Website",
      "price": "�3,500",
      "period": "one-time",
      "description": "Ideal for new Southampton eateries or those needing a professional online presence quickly.",
      "features": [
        "Bespoke 5-page design",
        "Mobile-responsive layout",
        "Digital menu integration",
        "Basic contact form",      ],
      "highlight": false,
      "cta": "Get Started"
    },
    {
      "name": "Signature Dish Website",
      "price": "�7,500",
      "period": "one-time",
      "description": "Our most popular package, designed for established Southampton restaurants seeking significant growth.",
      "features": [
        "Up to 10-page bespoke design",
        "Advanced online booking system",
        "Integrated online ordering platform",
        "Professional food photography session",
        "Local SEO optimisation",      ],
      "highlight": true,
      "cta": "Boost Bookings"
    },
    {
      "name": "Gourmet Bespoke Solution",
      "price": "Custom",
      "period": "one-time",
      "description": "For multi-location restaurants or unique culinary concepts requiring advanced features and integrations.",
      "features": [
        "Fully custom page count & design",
        "Advanced CRM integration",
        "Loyalty program integration",
        "Multi-language support",
        "Ongoing marketing strategy",      ],
      "highlight": false,
      "cta": "Request Quote"
    }
  ],
  "process": [
    {
      "step": "01",
      "title": "Discovery & Strategy",
      "description": "We begin by understanding your Southampton restaurant's unique brand, target audience, and business goals. This forms the blueprint for your bespoke website.",
      "duration": "Week 1"
    },
    {
      "step": "02",
      "title": "Design & Development",
      "description": "Our expert designers and developers bring your vision to life, crafting a visually stunning and highly functional website tailored to your culinary identity.",
      "duration": "Week 2-4"
    },
    {
      "step": "03",
      "title": "Content & Optimisation",
      "description": "We populate your site with engaging content, high-quality imagery, and implement robust SEO strategies to ensure your restaurant ranks highly in Southampton searches.",
      "duration": "Week 5-6"
    },
    {
      "step": "04",
      "title": "Launch & Growth",
      "description": "Your new website goes live! We provide ongoing support, analytics, and strategic advice to continuously drive traffic, bookings, and revenue for your business.",
      "duration": "Week 7+"
    }
  ],
  "featuredTestimonial": {
    "quote": "WebforLeads completely transformed our online presence. Our bookings from the website have soared, and the online ordering system is a game-changer for our takeaway service in Shirley. Truly exceptional results!",
    "name": "Sarah Jenkins",
    "role": "Owner",
    "company": "The Shirley Spice",
    "rating": 5,
    "result": "340%",
    "resultLabel": "Online Bookings Increase"
  },
  "testimonials": [
    {
      "quote": "Our new website by WebforLeads has made managing reservations at our Ocean Village restaurant so much easier. The design perfectly captures our brand, and we've seen a significant uplift in online enquiries.",
      "name": "Mark Davies",
      "role": "Manager",
      "company": "Ocean Grill",
      "industry": "Fine Dining",
      "metric": "315%",
      "metricLabel": "Online Enquiries",
      "avatar": "SMW",    },
    {
      "quote": "As a busy pub in Bitterne, we needed a website that could handle our events and food menus seamlessly. WebforLeads delivered a fantastic, easy-to-update site that our customers love.",
      "name": "Clare Thompson",
      "role": "Proprietor",
      "company": "The Bitterne Arms",
      "industry": "Pub & Gastropub",
      "metric": "200%",
      "metricLabel": "Website Traffic",
      "avatar": "CT",    },
    {
      "quote": "The team at WebforLeads understood our unique needs for our cafe in Portswood. The integrated online ordering has been a lifesaver, and the site looks absolutely brilliant on mobile.",
      "name": "David Price",
      "role": "Co-Founder",
      "company": "Portswood Coffee Co.",
      "industry": "Cafe & Bakery",
      "metric": "400%",
      "metricLabel": "Online Order Volume",
      "avatar": "DP",    }
  ],
  "faqCategories": [
    {
      "category": "Process & Timeline",
      "icon": <Rocket className="h-6 w-6" />,
      "questions": [
        {
          "q": "How long does it take to build a restaurant website?",
          "a": "Typically, a standard restaurant website takes 6-8 weeks from initial discovery to launch. More complex projects with custom integrations may take longer, but we always aim for efficient delivery."
        },
        {
          "q": "Can I update my menu and content myself?",
          "a": "Absolutely. All our restaurant websites are built with a user-friendly Content Management System (CMS) like WordPress, allowing you to easily update menus, daily specials, opening hours, and promotions without needing technical expertise."
        },
        {
          "q": "What is the process for getting started?",
          "a": "Our process begins with a detailed consultation to understand your vision. We then move to strategy, design, development, content population, and rigorous testing before launch. We keep you informed at every stage."
        }
      ]
    },
    {
      "category": "Services & Features",
      "icon": <Target className="h-6 w-6" />,
      "questions": [
        {
          "q": "Do you integrate online booking and ordering systems?",
          "a": "Yes, we specialise in integrating robust online booking and ordering systems tailored to your restaurant's needs. This can include direct reservations, takeaway, delivery, and table management solutions."
        },
        {
          "q": "Will my website be mobile-friendly?",
          "a": "Every website we design is built with a mobile-first approach, ensuring it is fully responsive and provides an optimal viewing and interaction experience across all devices, from smartphones to tablets and desktops."
        },
        {
          "q": "Can you help with food photography for my menu?",
          "a": "Yes, for our 'Signature Dish' and 'Gourmet Bespoke' packages, we offer professional food photography services to ensure your culinary creations are showcased in the most appealing way possible on your new website."
        }
      ]
    },
    {
      "category": "Technical & Pricing",
      "icon": <BarChart3 className="h-6 w-6" />,
      "questions": [
        {
          "q": "What are the typical costs for a restaurant website?",
          "a": "Our restaurant website design packages typically range from �3,500 for a foundational site to custom pricing for more complex, feature-rich solutions. We provide transparent quotes after understanding your specific requirements."
        },
        {
          "q": "Do you provide ongoing maintenance and support?",
          "a": "Yes, we offer comprehensive ongoing maintenance and support packages to ensure your website remains secure, up-to-date, and performing optimally. This includes security updates, backups, and technical assistance."
        },
        {
          "q": "Is SEO included in your restaurant website design service?",
          "a": "Absolutely. Local SEO is a core component of our service. We optimise your website's structure, content, and technical elements to help your Southampton restaurant rank higher in local search results and attract more diners."
        }
      ]
    }
  ],
  "finalCTA": {
    "badge": "Get Started",
    "title": "Ready to Serve Up Success in Southampton?",
    "description": "Don't let your competitors steal your diners. Partner with WebforLeads to create a high-performing restaurant website that drives bookings, boosts online orders, and elevates your brand. Contact us today for a free consultation."
  },
  "structuredData": {
    "serviceName": "Restaurant Website Design",
    "description": "WebforLeads provides bespoke, conversion-focused restaurant website design services in Southampton, UK. Specialising in online booking, ordering systems, and local SEO to attract more diners.",
    "priceRange": "�3500-�15000",
    "areaServed": "Southampton",
    "aggregateRating": {
      "ratingValue": "4.9",
      "reviewCount": "127"
    }
  }
};

export const metadata: Metadata = {
  title: serviceConfig.metaTitle,
  description: serviceConfig.metaDescription,
  keywords: serviceConfig.keywords.join(", "),
  openGraph: {
    title: serviceConfig.metaTitle,
    description: serviceConfig.metaDescription,
    type: 'website',
    url: `https://webforleads.uk/web-design/${serviceConfig.serviceSlug}`,
  },
  twitter: {
    card: 'summary_large_image',
    title: serviceConfig.metaTitle,
    description: serviceConfig.metaDescription,
  },
  alternates: {
    canonical: `https://webforleads.uk/web-design/${serviceConfig.serviceSlug}`,
  },
};

export default function RestaurantWebsiteDesignSouthamptonPage() {
  return <ServiceTemplate config={serviceConfig} />;
}


