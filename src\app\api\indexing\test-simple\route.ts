import { NextRequest, NextResponse } from 'next/server';
import { google } from 'googleapis';

export async function GET(request: NextRequest) {
  try {
    console.log('🧪 Starting simple authentication test...');
    
    // Get service account key
    const serviceAccountKeyString = process.env.GOOGLE_INDEXING_SERVICE_ACCOUNT_KEY;
    if (!serviceAccountKeyString) {
      return NextResponse.json({ success: false, error: 'Service account key not found' });
    }

    const serviceAccountKey = JSON.parse(serviceAccountKeyString);
    const privateKey = serviceAccountKey.private_key.replace(/\\n/g, '\n');
    
    console.log('🔑 Service Account Email:', serviceAccountKey.client_email);
    console.log('🌐 Project ID:', serviceAccountKey.project_id);
    console.log('🔗 Target URL:', process.env.NEXT_PUBLIC_SITE_URL);
    
    // Create JWT client
    const jwtClient = new google.auth.JWT({
      email: serviceAccountKey.client_email,
      key: privateKey,
      scopes: ['https://www.googleapis.com/auth/indexing'],
    });

    // Test authentication
    console.log('🔐 Testing JWT authentication...');
    await jwtClient.authorize();
    console.log('✅ JWT authentication successful');

    // Initialize indexing API
    const indexing = google.indexing({
      version: 'v3',
      auth: jwtClient,
    });

    // Test with your homepage first (most likely to work)
    const testUrl = process.env.NEXT_PUBLIC_SITE_URL || 'https://webforleads.uk';
    console.log(`📡 Testing API call with URL: ${testUrl}`);

    const response = await indexing.urlNotifications.publish({
      requestBody: {
        url: testUrl,
        type: 'URL_UPDATED',
      },
    });

    console.log('🎉 API call successful!');
    console.log('📥 Response:', JSON.stringify(response.data, null, 2));

    return NextResponse.json({
      success: true,
      message: 'Authentication and API call successful!',
      serviceAccount: serviceAccountKey.client_email,
      projectId: serviceAccountKey.project_id,
      testUrl: testUrl,
      response: response.data,
    });

  } catch (error: any) {
    console.error('❌ Test failed:', error);
    
    let errorDetails = {
      message: error.message,
      code: error.code,
      status: error.status,
    };

    // Check for specific error types
    if (error.message?.includes('authentication credential')) {
      errorDetails.diagnosis = 'Domain verification or service account permissions issue';
      errorDetails.solution = 'Wait 5-15 minutes for propagation, then verify domain URL format matches exactly';
    } else if (error.message?.includes('quota')) {
      errorDetails.diagnosis = 'API quota exceeded';
      errorDetails.solution = 'Wait until quota resets or request increase';
    } else if (error.message?.includes('API not enabled')) {
      errorDetails.diagnosis = 'Indexing API not enabled';
      errorDetails.solution = 'Enable API in Google Cloud Console';
    }

    return NextResponse.json({
      success: false,
      error: 'API test failed',
      details: errorDetails,
      serviceAccount: process.env.GOOGLE_INDEXING_SERVICE_ACCOUNT_KEY ? 'Present' : 'Missing',
      projectId: process.env.GOOGLE_INDEXING_PROJECT_ID || 'Not set',
      siteUrl: process.env.NEXT_PUBLIC_SITE_URL || 'Not set',
    });
  }
}
