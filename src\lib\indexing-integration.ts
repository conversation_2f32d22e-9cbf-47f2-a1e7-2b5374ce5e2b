import { googleIndexingService } from './google-indexing';
import { updateRouteIndexingStatus } from './route-registry';

// Integration functions for automatic indexing

export interface IndexingIntegrationOptions {
  autoSubmit?: boolean;
  batchSize?: number;
  delayBetweenRequests?: number;
}

// Submit a single page for indexing after generation
export async function submitPageForIndexing(
  slug: string, 
  url?: string,
  options: IndexingIntegrationOptions = {}
): Promise<{ success: boolean; error?: string }> {
  try {
    console.log(`🔄 Auto-submitting page for indexing: ${slug}`);
    
    // Update status to pending first
    updateRouteIndexingStatus(slug, 'pending');
    
    // Determine URL
    const pageUrl = url || `/${slug}`;
    
    // Submit to Google Indexing API
    const result = await googleIndexingService.submitUrl(pageUrl, 'URL_UPDATED');
    
    if (result.success) {
      // Update status to submitted
      updateRouteIndexingStatus(slug, 'submitted');
      console.log(`✅ Successfully submitted page for indexing: ${slug}`);
      return { success: true };
    } else {
      // Update status to error
      updateRouteIndexingStatus(slug, 'error', result.error);
      console.log(`❌ Failed to submit page for indexing: ${slug} - ${result.error}`);
      return { success: false, error: result.error };
    }
  } catch (error: any) {
    console.error(`❌ Error in submitPageForIndexing for ${slug}:`, error);
    updateRouteIndexingStatus(slug, 'error', error.message);
    return { success: false, error: error.message };
  }
}

// Submit multiple pages for indexing in batch
export async function submitPagesForIndexing(
  pages: Array<{ slug: string; url?: string }>,
  options: IndexingIntegrationOptions = {}
): Promise<{
  success: boolean;
  totalPages: number;
  successfulPages: number;
  failedPages: number;
  results: Array<{ slug: string; success: boolean; error?: string }>;
}> {
  const {
    batchSize = 50,
    delayBetweenRequests = 1000
  } = options;

  console.log(`🔄 Auto-submitting ${pages.length} pages for indexing in batches of ${batchSize}`);

  const results: Array<{ slug: string; success: boolean; error?: string }> = [];
  let successfulPages = 0;
  let failedPages = 0;

  // Process in batches
  for (let i = 0; i < pages.length; i += batchSize) {
    const batch = pages.slice(i, i + batchSize);
    console.log(`📝 Processing batch ${Math.floor(i / batchSize) + 1}: ${batch.length} pages`);

    // Mark all pages in batch as pending
    batch.forEach(page => {
      updateRouteIndexingStatus(page.slug, 'pending');
    });

    // Prepare URLs for batch submission
    const urls = batch.map(page => page.url || `/${page.slug}`);

    try {
      // Submit batch to Google Indexing API
      const batchResult = await googleIndexingService.submitBatch(urls, 'URL_UPDATED');

      // Process results
      batchResult.results.forEach((result, index) => {
        const page = batch[index];
        if (result.success) {
          updateRouteIndexingStatus(page.slug, 'submitted');
          successfulPages++;
          results.push({ slug: page.slug, success: true });
          console.log(`✅ Successfully submitted: ${page.slug}`);
        } else {
          updateRouteIndexingStatus(page.slug, 'error', result.error);
          failedPages++;
          results.push({ slug: page.slug, success: false, error: result.error });
          console.log(`❌ Failed to submit: ${page.slug} - ${result.error}`);
        }
      });

      // Add delay between batches
      if (i + batchSize < pages.length) {
        console.log(`⏳ Waiting ${delayBetweenRequests}ms before next batch...`);
        await new Promise(resolve => setTimeout(resolve, delayBetweenRequests));
      }
    } catch (error: any) {
      console.error(`❌ Batch submission failed:`, error);
      
      // Mark all pages in failed batch as error
      batch.forEach(page => {
        updateRouteIndexingStatus(page.slug, 'error', error.message);
        failedPages++;
        results.push({ slug: page.slug, success: false, error: error.message });
      });
    }
  }

  console.log(`📊 Batch indexing completed:`);
  console.log(`   Total: ${pages.length}`);
  console.log(`   Successful: ${successfulPages}`);
  console.log(`   Failed: ${failedPages}`);

  return {
    success: failedPages === 0,
    totalPages: pages.length,
    successfulPages,
    failedPages,
    results
  };
}

// Hook function to be called after bulk page generation completes
export async function onBulkPagesGenerated(
  jobId: string,
  generatedPages: Array<{ slug: string; url?: string }>,
  options: IndexingIntegrationOptions = { autoSubmit: true }
): Promise<void> {
  if (!options.autoSubmit) {
    console.log('🔄 Auto-submit disabled, skipping indexing integration');
    return;
  }

  console.log(`🔄 Starting indexing integration for job ${jobId} with ${generatedPages.length} pages`);

  try {
    const result = await submitPagesForIndexing(generatedPages, options);
    
    console.log(`📊 Indexing integration completed for job ${jobId}:`);
    console.log(`   Successful: ${result.successfulPages}/${result.totalPages}`);
    console.log(`   Failed: ${result.failedPages}/${result.totalPages}`);
    
    if (result.failedPages > 0) {
      console.log(`⚠️ Some pages failed to submit for indexing. Check the admin interface for details.`);
    }
  } catch (error) {
    console.error(`❌ Error in indexing integration for job ${jobId}:`, error);
  }
}

// Hook function to be called after a single page is generated
export async function onPageGenerated(
  slug: string,
  url?: string,
  options: IndexingIntegrationOptions = { autoSubmit: true }
): Promise<void> {
  if (!options.autoSubmit) {
    console.log('🔄 Auto-submit disabled, skipping indexing integration');
    return;
  }

  console.log(`🔄 Starting indexing integration for page: ${slug}`);

  try {
    const result = await submitPageForIndexing(slug, url, options);
    
    if (result.success) {
      console.log(`✅ Page successfully submitted for indexing: ${slug}`);
    } else {
      console.log(`❌ Failed to submit page for indexing: ${slug} - ${result.error}`);
    }
  } catch (error) {
    console.error(`❌ Error in indexing integration for page ${slug}:`, error);
  }
}

// Utility function to check if indexing is enabled
export function isIndexingEnabled(): boolean {
  try {
    const serviceAccountKey = process.env.GOOGLE_INDEXING_SERVICE_ACCOUNT_KEY;
    return !!serviceAccountKey;
  } catch {
    return false;
  }
}

// Utility function to get indexing configuration
export function getIndexingConfig(): IndexingIntegrationOptions {
  return {
    autoSubmit: isIndexingEnabled(),
    batchSize: 50,
    delayBetweenRequests: 2000, // 2 seconds between batches
  };
}
