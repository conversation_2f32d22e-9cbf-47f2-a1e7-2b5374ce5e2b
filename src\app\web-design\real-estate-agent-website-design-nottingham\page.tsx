import { Metadata } from 'next';
import ServiceTemplate from '../../site-pages/service-template';
import {
  Palette, Smartphone, TrendingUp, Search, Brain, Gauge,
  Target, Users, Rocket, BarChart3, Globe, Zap, Code,
  DollarSign, MousePointer, Share2, Heart, MessageCircle,
  Camera, Tablet, Monitor
} from "lucide-react";

// Real Estate Agent Website Design Service Configuration for real-estate-agent-website-design-nottingham
// Generated using Web Design Services template for WebforLeads
const serviceConfig = {
  "serviceName": "Real Estate Agent Website Design",
  "serviceSlug": "real-estate-agent-website-design-nottingham",
  "metaTitle": "Real Estate Agent Website Design Nottingham | WebforLeads",
  "metaDescription": "Elevate your Nottingham real estate agency with a high-converting website from WebforLeads. Bespoke designs for lead generation & property showcases.",
  "keywords": [
    "real estate agent website design nottingham",
    "nottingham property website",
    "estate agent web design uk",
    "web design for letting agents",
    "property portal development",
    "nottingham digital marketing agency",
    "real estate lead generation website",
    "bespoke property websites",  ],
  "heroTitle": "High-Converting Real Estate Agent Website Design in Nottingham",
  "heroSubtitle": "Attract More Buyers & Sellers Across Nottinghamshire",
  "heroDescription": "WebforLeads crafts bespoke, lead-generating websites for real estate agents and letting agencies across Nottingham, from West Bridgford to Mapperley. Our designs are engineered to showcase your properties, capture valuable leads, and establish your agency as the premier choice in the East Midlands property market.",
  "heroBadgeText": "Nottingham Property Experts",
  "stats": [
    {
      "number": "340%",
      "label": "Average Lead Increase",
      "icon": <TrendingUp className="h-6 w-6" />
    },
    {
      "number": "£1.8M+",
      "label": "Client Revenue Generated",
      "icon": <DollarSign className="h-6 w-6" />
    },
    {
      "number": "96%",
      "label": "Client Satisfaction Rate",
      "icon": <Heart className="h-6 w-6" />
    },
    {
      "number": "24hrs",
      "label": "Initial Response Time",
      "icon": <MessageCircle className="h-6 w-6" />
    }
  ],
  "designFeatures": [
    {
      "icon": <Brain className="h-8 w-8" />,
      "title": "Intuitive Property Search",
      "description": "Seamlessly integrate advanced search filters, map views, and saved searches for an unparalleled user experience, making property discovery effortless for your Nottingham clients.",
      "highlight": "Effortless Discovery"
    },
    {
      "icon": <Gauge className="h-8 w-8" />,
      "title": "Lead Capture Optimisation",
      "description": "Implement strategic CTAs, enquiry forms, and valuation tools designed to convert website visitors into qualified leads for your Nottinghamshire property listings.",
      "highlight": "Convert Visitors"
    },
    {
      "icon": <Target className="h-6 w-6" />,
      "title": "CRM & MLS Integration",
      "description": "Connect your website directly with leading CRM systems and property listing feeds (e.g., Rightmove, Zoopla) for automated updates and streamlined operations.",
      "highlight": "Streamlined Operations"
    },
    {
      "icon": <Smartphone className="h-6 w-6" />,
      "title": "Mobile-First Responsiveness",
      "description": "Ensure your website looks flawless and performs perfectly on any device, from desktop to smartphone, critical for on-the-go property browsing in Nottingham.",
      "highlight": "Any Device, Anywhere"
    }
  ],
  "features": [
    {
      "icon": <Search className="h-6 w-6" />,
      "title": "SEO-Optimised for Local Search",
      "description": "Rank higher for Nottingham-specific property searches, ensuring your agency is found by local buyers and sellers in areas like Beeston and Sherwood."
    },
    {
      "icon": <Palette className="h-6 w-6" />,
      "title": "Bespoke Branding & UI/UX",
      "description": "Reflect your agency's unique identity with a custom-designed interface that provides an exceptional user experience, setting you apart in the Nottingham market."
    },
    {
      "icon": <Code className="h-6 w-6" />,
      "title": "Secure & Scalable Architecture",
      "description": "Built on robust, secure platforms, our websites are designed to grow with your agency, handling increasing traffic and property listings with ease."
    },
    {
      "icon": <Zap className="h-6 w-6" />,
      "title": "High-Speed Performance",
      "description": "Optimised for rapid loading times, ensuring visitors stay engaged and don't bounce due to slow pages, crucial for competitive Nottingham property searches."
    },
    {
      "icon": <BarChart3 className="h-6 w-6" />,
      "title": "Advanced Analytics & Reporting",
      "description": "Gain deep insights into visitor behaviour, lead sources, and property interest, empowering data-driven decisions for your Nottingham agency."
    },
    {
      "icon": <Users className="h-6 w-6" />,
      "title": "Client Portal Integration",
      "description": "Offer a secure client login area for viewing property updates, documents, and communication, enhancing client satisfaction and retention."
    }
  ],
  "packages": [
    {
      "name": "Foundation Property Site",
      "price": "£3,500",
      "period": "one-time",
      "description": "Ideal for new or smaller Nottingham estate agencies needing a professional online presence to showcase properties and capture initial leads.",
      "features": [
        "Bespoke 5-page design",
        "Mobile responsive",
        "Basic SEO setup",
        "Property listing integration (manual upload)",
        "Contact forms",      ],
      "highlight": false,
      "cta": "Get Started"
    },
    {
      "name": "Growth Agent Platform",
      "price": "£7,500",
      "period": "one-time",
      "description": "Our most popular choice for established Nottingham agencies seeking advanced lead generation, CRM integration, and automated property feeds.",
      "features": [
        "All Foundation features",
        "Up to 15 pages",
        "Automated property feed integration (Rightmove/Zoopla)",
        "Advanced lead capture forms",
        "CRM integration",
        "Blog/News section",      ],
      "highlight": true,
      "cta": "Boost Your Leads"
    },
    {
      "name": "Enterprise Property Portal",
      "price": "Custom",
      "period": "one-time",
      "description": "For large agencies or developers requiring a comprehensive, custom-built property portal with unique features and integrations.",
      "features": [
        "All Growth features",
        "Unlimited pages",
        "Custom API integrations",
        "Multi-branch support",
        "Advanced user management",
        "Bespoke client portal",      ],
      "highlight": false,
      "cta": "Request Consultation"
    }
  ],
  "process": [
    {
      "step": "01",
      "title": "Discovery & Strategy",
      "description": "We deep-dive into your Nottingham agency's goals, target market (e.g., buyers in The Park, sellers in Wollaton), and unique selling propositions to define a clear website strategy.",
      "duration": "Week 1"
    },
    {
      "step": "02",
      "title": "Design & Development",
      "description": "Our expert team crafts a bespoke, conversion-focused design, followed by robust development, integrating property feeds and lead capture mechanisms.",
      "duration": "Week 2-3"
    },
    {
      "step": "03",
      "title": "Content & Optimisation",
      "description": "We assist with compelling property descriptions and local area guides, optimising all content for Nottingham-specific SEO and user engagement.",
      "duration": "Week 4-5"
    },
    {
      "step": "04",
      "title": "Launch & Support",
      "description": "After rigorous testing, your new high-performance real estate website goes live, backed by our ongoing support and performance monitoring.",
      "duration": "Week 6+"
    }
  ],
  "featuredTestimonial": {
    "quote": "WebforLeads transformed our online presence. Our new website is not only stunning but has significantly boosted our property enquiries across Nottingham. Their understanding of the local market is exceptional.",
    "name": "Sarah Jenkins",
    "role": "Director",
    "company": "Nottingham Homes & Lettings",
    "rating": 5,
    "result": "340%",
    "resultLabel": "Increase in Enquiries"
  },
  "testimonials": [
    {
      "quote": "Our old site was stagnant. WebforLeads delivered a dynamic, lead-generating platform that truly reflects our brand in West Bridgford. Highly recommend for any estate agent.",
      "name": "Mark Davies",
      "role": "Managing Partner",
      "company": "Bridgford Property Group",
      "industry": "Real Estate",
      "metric": "315%",
      "metricLabel": "Website Leads",
      "avatar": "FPS",    },
    {
      "quote": "From initial concept to launch, WebforLeads provided an outstanding service. Our new website has made property listings effortless and client engagement soar in Beeston.",
      "name": "Emily Roberts",
      "role": "Senior Agent",
      "company": "Beeston Estates",
      "industry": "Property Sales",
      "metric": "200%",
      "metricLabel": "Online Viewings",
      "avatar": "ER",    },
    {
      "quote": "The team at WebforLeads truly understood our needs as a commercial property agency in Nottingham city centre. Our new site is professional, fast, and converting well.",
      "name": "David Price",
      "role": "Commercial Director",
      "company": "City Centre Commercials",
      "industry": "Commercial Property",
      "metric": "400%",
      "metricLabel": "Valuation Requests",
      "avatar": "DP",    }
  ],
  "faqCategories": [
    {
      "category": "Process & Timeline",
      "icon": <Rocket className="h-6 w-6" />,
      "questions": [
        {
          "q": "How long does it take to build a real estate website?",
          "a": "Typically, a bespoke real estate website project with WebforLeads takes between 6-10 weeks, depending on complexity and features like CRM integration or custom property search functions."
        },
        {
          "q": "Can you integrate with our existing CRM system?",
          "a": "Yes, we specialise in integrating your new website with popular real estate CRM systems and property management software to streamline your operations and lead management."
        },
        {
          "q": "What is the process for property listing integration?",
          "a": "We can set up automated feeds from major property portals like Rightmove and Zoopla, or integrate with your internal property management system for seamless, real-time listing updates."
        }
      ]
    },
    {
      "category": "Services & Features",
      "icon": <Target className="h-6 w-6" />,
      "questions": [
        {
          "q": "Is the website optimised for local Nottingham searches?",
          "a": "Absolutely. We implement advanced local SEO strategies to ensure your agency ranks prominently for Nottingham-specific property searches, targeting areas like Mapperley, Sherwood, and The Park."
        },
        {
          "q": "Can we manage property listings ourselves after launch?",
          "a": "Yes, all our websites come with a user-friendly Content Management System (CMS) that allows your team to easily add, edit, and remove property listings, update content, and manage enquiries."
        },
        {
          "q": "Do you provide ongoing support and maintenance?",
          "a": "Yes, we offer comprehensive post-launch support and maintenance packages to ensure your website remains secure, up-to-date, and performing optimally, allowing you to focus on selling properties."
        }
      ]
    },
    {
      "category": "Technical & Pricing",
      "icon": <BarChart3 className="h-6 w-6" />,
      "questions": [
        {
          "q": "What is the typical cost for a real estate agent website?",
          "a": "Our real estate website design projects typically range from £3,500 for a foundational site to custom pricing for advanced, enterprise-level property portals, depending on your specific requirements."
        },
        {
          "q": "Are your websites mobile-friendly?",
          "a": "Every website we build is designed with a mobile-first approach, ensuring it provides an exceptional user experience and looks perfect on all devices, from smartphones to desktops."
        },
        {
          "q": "What about website security and hosting?",
          "a": "We build on secure, robust platforms and can advise on or provide high-performance, secure hosting solutions tailored for real estate websites, protecting your data and your clients' information."
        }
      ]
    }
  ],
  "finalCTA": {
    "badge": "Elevate Your Agency",
    "title": "Ready to Dominate the Nottingham Property Market?",
    "description": "Partner with WebforLeads to build a high-performing real estate website that attracts more leads, showcases your properties effectively, and establishes your agency as a leader in Nottingham and beyond."
  },
  "structuredData": {
    "serviceName": "Real Estate Agent Website Design",
    "description": "WebforLeads provides bespoke, high-converting website design services for real estate agents and letting agencies in Nottingham, focusing on lead generation, property showcases, and local SEO.",
    "priceRange": "£3500-Custom",
    "areaServed": "Nottingham",
    "aggregateRating": {
      "ratingValue": "4.9",
      "reviewCount": "127"
    }
  }
};

export const metadata: Metadata = {
  title: serviceConfig.metaTitle,
  description: serviceConfig.metaDescription,
  keywords: serviceConfig.keywords.join(", "),
  openGraph: {
    title: serviceConfig.metaTitle,
    description: serviceConfig.metaDescription,
    type: 'website',
    url: `https://webforleads.uk/web-design/${serviceConfig.serviceSlug}`,
  },
  twitter: {
    card: 'summary_large_image',
    title: serviceConfig.metaTitle,
    description: serviceConfig.metaDescription,
  },
  alternates: {
    canonical: `https://webforleads.uk/web-design/${serviceConfig.serviceSlug}`,
  },
};

export default function RealEstateAgentWebsiteDesignNottinghamPage() {
  return <ServiceTemplate config={serviceConfig} />;
}


